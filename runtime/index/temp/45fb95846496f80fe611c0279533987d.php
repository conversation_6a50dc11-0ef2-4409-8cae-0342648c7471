<?php /*a:1:{s:74:"/mnt/sdc/data/wwwroot/news.test.jijiaox.com/app/index/view/index/test.html";i:1752146513;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlentities((string) $title); ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .data { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .back { margin-top: 20px; }
        .back a { color: #007bff; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1><?php echo htmlentities((string) $title); ?></h1>
        <div class="data">
            <h3>测试数据：</h3>
            <p><strong>时间戳：</strong><?php echo htmlentities((string) $data['timestamp']); ?></p>
            <p><strong>随机数：</strong><?php echo htmlentities((string) $data['random']); ?></p>
            <p><strong>用户代理：</strong><?php echo htmlentities((string) $data['user_agent']); ?></p>
        </div>
        
        <div class="back">
            <a href="/index/index/index">← 返回首页</a>
        </div>
    </div>
</body>
</html>
