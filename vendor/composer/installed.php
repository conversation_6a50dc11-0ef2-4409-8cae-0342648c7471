<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => 'topthink/think',
        'dev' => true,
    ),
    'versions' => array(
        'doctrine/annotations' => array(
            'pretty_version' => '1.14.3',
            'version' => '1.14.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/annotations',
            'aliases' => array(),
            'reference' => 'fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af',
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'reference' => 'c268e882d4dbdd85e36e4ad69e02dc284f89d229',
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '1.1.10',
            'version' => '1.1.10.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'reference' => '3239285c825c152bcc315fe0e87d6b55f5972ed1',
            'dev_requirement' => false,
        ),
        'league/flysystem-cached-adapter' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-cached-adapter',
            'aliases' => array(),
            'reference' => 'd1925efb2207ac4be3ad0c40b8277175f99ffaff',
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.15.0',
            'version' => '1.15.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'reference' => 'ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301',
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => '79dff0b268932c640297f5208d6298f71855c03e',
            'dev_requirement' => false,
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'dev_requirement' => true,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'dev_requirement' => true,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'reference' => '548f6760c54197b1084e1e5c71f6d9d523f2f78e',
            'dev_requirement' => true,
        ),
        'topthink/framework' => array(
            'pretty_version' => 'v8.1.2',
            'version' => '8.1.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/framework',
            'aliases' => array(),
            'reference' => '8faec5c9b7a7f2a66ca3140a57e81bd6cd37567c',
            'dev_requirement' => false,
        ),
        'topthink/think' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'topthink/think-captcha' => array(
            'pretty_version' => 'v3.0.11',
            'version' => '3.0.11.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-captcha',
            'aliases' => array(),
            'reference' => '4f24f560a31011329e3d144732e5370d7676b3fb',
            'dev_requirement' => false,
        ),
        'topthink/think-container' => array(
            'pretty_version' => 'v3.0.1',
            'version' => '3.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-container',
            'aliases' => array(),
            'reference' => 'a24d442a02fb2a4716de232ff1a4f006c178a370',
            'dev_requirement' => false,
        ),
        'topthink/think-dumper' => array(
            'pretty_version' => 'v1.0.5',
            'version' => '1.0.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-dumper',
            'aliases' => array(),
            'reference' => 'eba662a1843d5db68059050c530f7d43287289fc',
            'dev_requirement' => true,
        ),
        'topthink/think-filesystem' => array(
            'pretty_version' => 'v2.0.3',
            'version' => '2.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-filesystem',
            'aliases' => array(),
            'reference' => 'e8e51adb9f3a3f3aac2aa3ef73b7b439100f777d',
            'dev_requirement' => false,
        ),
        'topthink/think-helper' => array(
            'pretty_version' => 'v3.1.11',
            'version' => '3.1.11.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-helper',
            'aliases' => array(),
            'reference' => '1d6ada9b9f3130046bf6922fe1bd159c8d88a33c',
            'dev_requirement' => false,
        ),
        'topthink/think-multi-app' => array(
            'pretty_version' => 'v1.1.1',
            'version' => '1.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-multi-app',
            'aliases' => array(),
            'reference' => 'f93c604d5cfac2b613756273224ee2f88e457b88',
            'dev_requirement' => false,
        ),
        'topthink/think-orm' => array(
            'pretty_version' => 'v4.0.40',
            'version' => '4.0.40.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-orm',
            'aliases' => array(),
            'reference' => '1637860ff736859058f0a5003c7cc719c2068dbc',
            'dev_requirement' => false,
        ),
        'topthink/think-template' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-template',
            'aliases' => array(),
            'reference' => '0b88bd449f0f7626dd75b05f557c8bc208c08b0c',
            'dev_requirement' => false,
        ),
        'topthink/think-trace' => array(
            'pretty_version' => 'v1.6',
            'version' => '1.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-trace',
            'aliases' => array(),
            'reference' => '136cd5d97e8bdb780e4b5c1637c588ed7ca3e142',
            'dev_requirement' => true,
        ),
        'topthink/think-validate' => array(
            'pretty_version' => 'v3.0.7',
            'version' => '3.0.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-validate',
            'aliases' => array(),
            'reference' => '85063f6d4ef8ed122f17a36179dc3e0949b30988',
            'dev_requirement' => false,
        ),
        'topthink/think-view' => array(
            'pretty_version' => 'v2.0.0',
            'version' => '2.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-view',
            'aliases' => array(),
            'reference' => 'd2a076011c96d2edd8016703a827fb54b2683c62',
            'dev_requirement' => false,
        ),
    ),
);
