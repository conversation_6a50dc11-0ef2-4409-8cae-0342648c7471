# 门户配置系统使用指南

## 📋 系统概述

门户配置系统是一个功能完整的网站配置管理解决方案，支持KV形式的基础配置存储和可视化的模块配置管理。系统采用ThinkPHP 8框架开发，前端使用layui框架，提供了直观易用的管理界面。

## 🏗️ 系统架构

### 后端架构
- **控制器层**: `PortalConfigController` 和 `PortalModuleController`
- **服务层**: `PortalConfigService` 和 `PortalModuleService`
- **模型层**: `PortalConfig` 和 `PortalModule`
- **数据库**: MySQL，支持JSON字段存储复杂配置

### 前端架构
- **基础配置管理**: `portal_config.html`
- **模块配置管理**: `portal_module.html`
- **UI框架**: layui + 自定义样式
- **拖拽排序**: Sortable.js

## 🚀 快速开始

### 1. 数据库初始化

执行SQL文件创建必要的数据表：

```bash
mysql -u用户名 -p数据库名 < database/portal_system.sql
```

### 2. 访问管理界面

- **基础配置管理**: `http://your-domain/admin/portal_config.html`
- **模块配置管理**: `http://your-domain/admin/portal_module.html`

### 3. 权限要求

- **基础配置**: 需要管理员权限，创建/删除需要超级管理员权限
- **模块配置**: 需要管理员权限
- **公开接口**: 获取启用模块列表无需权限

## 📊 基础配置管理

### 配置类型

系统支持四种配置类型：

1. **string**: 字符串类型，适用于文本配置
2. **number**: 数字类型，自动转换为整数或浮点数
3. **boolean**: 布尔类型，支持 true/false
4. **json**: JSON对象类型，支持复杂数据结构

### 配置分组

配置可以按分组进行管理，常用分组：

- `basic`: 基础配置（网站名称、Logo等）
- `seo`: SEO相关配置
- `upload`: 上传配置
- `feature`: 功能开关
- `performance`: 性能配置
- `theme`: 主题配置

### API接口

#### 获取配置值
```php
use app\api\service\PortalConfigService;

$configService = new PortalConfigService();

// 获取单个配置
$siteName = $configService->getConfigValue('site_name', '默认网站名');

// 获取分组配置
$basicConfigs = $configService->getConfigByGroup('basic');
```

#### 设置配置值
```php
// 创建配置
$configService->createConfig([
    'config_key' => 'new_config',
    'config_value' => 'config value',
    'config_type' => 'string',
    'group_name' => 'custom',
    'description' => '自定义配置'
]);

// 批量设置
$configService->batchSetConfigs([
    'config1' => 'value1',
    'config2' => 123,
    'config3' => true
], 'custom');
```

## 🧩 模块配置管理

### 模块特性

- **列数支持**: 每个模块支持1列或2列布局
- **配置灵活**: 使用JSON格式存储模块配置
- **拖拽排序**: 支持可视化拖拽调整模块顺序
- **启用控制**: 可以随时启用或禁用模块

### 预置模块

系统预置了6个常用模块：

1. **news_banner**: 新闻轮播（单列）
2. **hot_news**: 热门新闻（双列）
3. **category_nav**: 分类导航（单列）
4. **latest_articles**: 最新文章（双列）
5. **sidebar_ads**: 侧边广告（单列）
6. **footer_links**: 底部链接（单列）

### 模块配置示例

```json
{
  "limit": 10,
  "show_image": true,
  "show_date": true,
  "auto_play": true,
  "interval": 3000,
  "height": "400px"
}
```

### API接口

#### 获取模块配置
```php
use app\api\service\PortalModuleService;

$moduleService = new PortalModuleService();

// 获取启用的模块列表
$enabledModules = $moduleService->getEnabledModules();

// 获取模块配置
$bannerConfig = $moduleService->getModuleConfig('news_banner');
$autoPlay = $moduleService->getModuleConfig('news_banner', 'auto_play', false);
```

#### 设置模块配置
```php
// 创建模块
$moduleService->createModule([
    'module_name' => 'custom_module',
    'module_title' => '自定义模块',
    'module_description' => '这是一个自定义模块',
    'column_count' => 1,
    'config_data' => [
        'limit' => 5,
        'show_title' => true
    ]
]);

// 更新模块配置
$moduleService->setModuleConfig('custom_module', [
    'limit' => 10,
    'show_title' => false,
    'new_option' => 'value'
]);
```

## 🔧 高级功能

### 缓存机制

系统内置了缓存机制，提高配置读取性能：

- **配置缓存**: 单个配置和分组配置都有缓存
- **模块缓存**: 启用模块列表和模块配置有缓存
- **缓存时间**: 默认1小时，可在服务类中调整
- **缓存清理**: 支持手动清除缓存

### 权限控制

系统集成了现有的权限系统：

- **super_admin**: 超级管理员，拥有所有权限
- **content_admin**: 内容管理员，可以管理模块配置
- **系统配置**: 只有超级管理员可以删除系统配置

### 数据验证

- **配置键名**: 必须以字母开头，只能包含字母、数字、下划线
- **模块名称**: 同配置键名规则
- **JSON格式**: 自动验证JSON配置的格式正确性
- **列数限制**: 模块列数只能是1或2

## 🎨 前端集成

### 获取配置数据

```javascript
// 获取启用的模块列表
fetch('/api/portal/modules/enabled')
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            renderModules(data.data);
        }
    });

// 获取配置分组
fetch('/api/admin/portal/configs/group/basic', {
    headers: {
        'Authorization': 'Bearer ' + token
    }
})
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            applyBasicConfig(data.data);
        }
    });
```

### 模块渲染示例

```javascript
function renderModules(modules) {
    modules.forEach(module => {
        const container = document.getElementById('module-container');
        const moduleDiv = document.createElement('div');
        
        // 根据列数设置样式
        if (module.column_count === 2) {
            moduleDiv.className = 'module-two-column';
        } else {
            moduleDiv.className = 'module-one-column';
        }
        
        // 根据模块配置渲染内容
        const config = module.config_data || {};
        moduleDiv.innerHTML = renderModuleContent(module.module_name, config);
        
        container.appendChild(moduleDiv);
    });
}
```

## 🔍 故障排除

### 常见问题

1. **权限不足**: 确保用户有相应的管理员权限
2. **JSON格式错误**: 检查配置数据的JSON格式是否正确
3. **缓存问题**: 尝试清除配置缓存
4. **模块不显示**: 检查模块是否已启用

### 调试技巧

1. **查看日志**: 检查ThinkPHP的日志文件
2. **API测试**: 使用Postman等工具测试API接口
3. **浏览器控制台**: 查看前端JavaScript错误
4. **数据库检查**: 直接查询数据库确认数据状态

## 📈 性能优化

### 缓存策略

- 合理设置缓存过期时间
- 在配置更新时及时清除相关缓存
- 考虑使用Redis等外部缓存

### 数据库优化

- 为常用查询字段添加索引
- 定期清理无用的配置数据
- 考虑配置数据的归档策略

## 🔮 扩展开发

### 添加新的配置类型

1. 在模型中扩展 `convertValue` 方法
2. 在前端添加对应的输入控件
3. 更新验证规则

### 自定义模块类型

1. 创建新的模块配置模板
2. 实现对应的前端渲染逻辑
3. 添加模块特定的配置选项

### 集成第三方服务

1. 扩展配置类型支持外部API
2. 实现配置同步机制
3. 添加配置导入导出功能

---

## 📞 技术支持

如有问题或建议，请联系开发团队或查看项目文档。

**版本**: v1.0.0  
**更新时间**: 2025-01-10  
**兼容性**: ThinkPHP 8.0+, PHP 8.0+
