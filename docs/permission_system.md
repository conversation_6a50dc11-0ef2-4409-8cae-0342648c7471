# 权限系统说明文档

## 概述

本系统使用基于注解的方法级权限控制，支持三种权限级别：
- **公开接口**：无需权限注解，任何人都可以访问
- **管理员接口**：使用 `#[RequireRole()]` 注解，需要登录的管理员
- **超级管理员接口**：使用 `#[RequireRole(isSuper: true)]` 注解，需要超级管理员权限

## 权限注解使用

### 1. 公开接口（无需权限）

```php
/**
 * 公开接口，无需任何权限
 */
public function publicMethod(): Response
{
    // 任何人都可以访问
}
```

### 2. 管理员接口（需要登录）

```php
/**
 * 需要登录的管理员权限
 */
#[RequireRole(message: '此操作需要管理员权限')]
public function adminMethod(): Response
{
    // 需要登录的管理员才能访问
}
```

### 3. 超级管理员接口

```php
/**
 * 需要超级管理员权限
 */
#[RequireRole(isSuper: true, message: '此操作需要超级管理员权限')]
public function superAdminMethod(): Response
{
    // 只有超级管理员才能访问
}
```

### 4. 兼容旧版本（角色数组）

```php
/**
 * 兼容旧版本的角色数组格式
 */
#[RequireRole(['content_admin', 'super_admin'], '需要内容管理员或超级管理员权限')]
public function legacyMethod(): Response
{
    // 兼容旧版本的使用方式
}
```

## 注解参数说明

### RequireRole 注解参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `roles` | `array\|string\|null` | `null` | 兼容旧版本的角色数组 |
| `isSuper` | `bool` | `false` | 是否需要超级管理员权限 |
| `message` | `string` | `'权限不足'` | 权限不足时的错误消息 |

### 使用示例

```php
// 基础管理员权限
#[RequireRole()]

// 基础管理员权限 + 自定义消息
#[RequireRole(message: '查看此内容需要管理员权限')]

// 超级管理员权限
#[RequireRole(isSuper: true)]

// 超级管理员权限 + 自定义消息
#[RequireRole(isSuper: true, message: '删除操作需要超级管理员权限')]

// 兼容旧版本
#[RequireRole(['content_admin', 'super_admin'])]
```

## 权限级别说明

### 1. 公开接口
- **特征**：方法上没有 `RequireRole` 注解
- **访问条件**：无需任何权限
- **适用场景**：公开API、前端展示数据等

### 2. 管理员接口
- **特征**：使用 `#[RequireRole()]` 或 `#[RequireRole(message: '...')]`
- **访问条件**：需要通过 `AuthMiddleware` 验证登录状态
- **适用场景**：一般的管理功能、数据查看等

### 3. 超级管理员接口
- **特征**：使用 `#[RequireRole(isSuper: true)]`
- **访问条件**：需要用户角色为 `super_admin`
- **适用场景**：删除操作、系统管理、敏感配置等

## 中间件工作流程

### AuthMiddleware（认证中间件）
1. 检查是否在排除路由列表中
2. 验证用户登录状态
3. 刷新用户信息

### RoleMiddleware（权限中间件）
1. 检查是否在排除路由列表中
2. 解析控制器和方法的权限注解
3. 根据注解类型进行权限验证：
   - 无注解：直接通过（公开接口）
   - 有注解且 `isSuper: true`：检查超级管理员权限
   - 有注解且 `isSuper: false`：检查是否为登录管理员
   - 兼容旧版本角色数组：检查用户是否拥有指定角色

## 实际应用示例

### 文章分类管理控制器

```php
class ArticleCategoryController
{
    // 查看分类列表 - 管理员权限
    #[RequireRole(message: '查看分类列表需要管理员权限')]
    public function index(): Response { }
    
    // 创建分类 - 管理员权限
    #[RequireRole(message: '创建分类需要管理员权限')]
    public function create(): Response { }
    
    // 删除分类 - 超级管理员权限
    #[RequireRole(isSuper: true, message: '删除分类需要超级管理员权限')]
    public function delete(): Response { }
}
```

### 公开分类控制器

```php
class PublicCategoryController
{
    // 公开菜单树 - 无需权限
    public function publicTree(): Response { }
    
    // 管理员统计 - 管理员权限
    #[RequireRole(message: '查看统计需要管理员权限')]
    public function adminStats(): Response { }
    
    // 系统管理 - 超级管理员权限
    #[RequireRole(isSuper: true, message: '系统管理需要超级管理员权限')]
    public function systemManage(): Response { }
}
```

## 错误响应格式

### 401 未认证
```json
{
    "code": 401,
    "message": "请先登录",
    "data": null
}
```

### 403 权限不足
```json
{
    "code": 403,
    "message": "需要超级管理员权限",
    "data": null
}
```

## 配置说明

### 排除路由配置

在 `AuthMiddleware` 和 `RoleMiddleware` 中可以配置不需要权限验证的路由：

```php
$excludeRoutes = [
    'api/admin/auth/login',
    'api/admin/auth/check',
    'api/test/bean',
    'api/test/service',
    'api/',
    'api/test'
];
```

### 控制器映射配置

在 `RoleMiddleware` 中配置控制器名称映射：

```php
$controllerMap = [
    'auth' => 'AuthController',
    'users' => 'UserController',
    'categories' => 'ArticleCategoryController',
];
```

## 最佳实践

1. **明确权限级别**：根据功能的敏感程度选择合适的权限级别
2. **自定义错误消息**：为用户提供清晰的权限说明
3. **公开接口最小化**：只有真正需要公开的接口才不加权限注解
4. **超级管理员权限谨慎使用**：只对删除、系统管理等高风险操作使用
5. **保持一致性**：同类型的操作使用相同的权限级别

## 升级指南

### 从旧版本升级

1. **移除类级别注解**：将类级别的 `RequireRole` 注解移除
2. **添加方法级别注解**：为每个需要权限的方法添加适当的注解
3. **简化权限配置**：使用新的 `isSuper` 参数替代角色数组
4. **更新路由配置**：确保中间件正确配置

### 兼容性说明

- 完全兼容旧版本的角色数组格式
- 可以逐步迁移到新的简化格式
- 中间件自动处理不同的注解格式
