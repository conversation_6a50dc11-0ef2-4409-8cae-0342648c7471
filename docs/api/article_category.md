# 文章分类管理 API 文档

## 概述

文章分类管理API提供了完整的多级分类管理功能，支持三种分类类型：
- **list（列表）**: 可以创建子分类的分类类型
- **single（单章）**: 不能创建子分类的页面类型
- **link（连接）**: 外部链接类型，不能创建子分类

## 权限说明

本API使用方法级权限控制，不同接口有不同的权限要求：

- **管理员权限**：需要登录的管理员，在请求头中包含认证信息
- **超级管理员权限**：需要超级管理员角色
- **公开接口**：无需权限（如有提供）

### 认证方式

需要权限的接口需要在请求头中包含认证信息：

```
Authorization: Bearer {token}
```

## 基础信息

- **Base URL**: `/admin/categories`
- **Content-Type**: `application/json`
- **响应格式**: JSON

## 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

## API 接口列表

### 1. 获取分类列表

**接口地址**: `GET /admin/categories`
**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认15 |
| name | string | 否 | 分类名称（模糊搜索） |
| type | string | 否 | 分类类型（list/single/link） |
| parent_id | int | 否 | 父分类ID |
| is_show | int | 否 | 显示状态（0隐藏/1显示） |
| level | int | 否 | 层级深度 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "parent_id": 0,
        "name": "新闻资讯",
        "slug": "news",
        "type": "list",
        "type_text": "列表",
        "description": "最新的新闻资讯内容",
        "link_url": null,
        "cover_image": null,
        "sort_order": 100,
        "level": 1,
        "path": "1",
        "path_array": [1],
        "is_show": 1,
        "show_text": "显示",
        "can_have_children": true,
        "is_top_level": true,
        "seo_title": "新闻资讯 - 最新资讯",
        "seo_keywords": "新闻,资讯,最新",
        "seo_description": "提供最新的新闻资讯内容",
        "create_time": "2024-01-01 12:00:00",
        "update_time": "2024-01-01 12:00:00"
      }
    ],
    "total": 18,
    "page": 1,
    "limit": 15,
    "pages": 2
  }
}
```

### 2. 获取分类详情

**接口地址**: `GET /admin/categories/{id}`
**权限要求**: 管理员权限

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 分类ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "parent_id": 0,
    "name": "新闻资讯",
    "slug": "news",
    "type": "list",
    "type_text": "列表",
    "description": "最新的新闻资讯内容",
    "link_url": null,
    "cover_image": null,
    "sort_order": 100,
    "level": 1,
    "path": "1",
    "path_array": [1],
    "is_show": 1,
    "show_text": "显示",
    "can_have_children": true,
    "is_top_level": true,
    "seo_title": "新闻资讯 - 最新资讯",
    "seo_keywords": "新闻,资讯,最新",
    "seo_description": "提供最新的新闻资讯内容",
    "create_time": "2024-01-01 12:00:00",
    "update_time": "2024-01-01 12:00:00"
  }
}
```

### 3. 创建分类

**接口地址**: `POST /admin/categories`
**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| parent_id | int | 否 | 父分类ID，默认0（顶级） |
| name | string | 是 | 分类名称，最大100字符 |
| slug | string | 是 | 分类别名，最大100字符，唯一 |
| type | string | 否 | 分类类型，默认list |
| description | string | 否 | 分类描述 |
| link_url | string | 否 | 链接地址（type为link时必填） |
| cover_image | string | 否 | 封面图片路径 |
| sort_order | int | 否 | 排序权重，默认0 |
| is_show | int | 否 | 显示状态，默认1 |
| seo_title | string | 否 | SEO标题 |
| seo_keywords | string | 否 | SEO关键词 |
| seo_description | string | 否 | SEO描述 |

**请求示例**:
```json
{
  "parent_id": 0,
  "name": "技术文档",
  "slug": "tech-docs",
  "type": "list",
  "description": "技术相关的文档和教程",
  "sort_order": 90,
  "is_show": 1,
  "seo_title": "技术文档 - 开发教程",
  "seo_keywords": "技术,文档,教程,开发",
  "seo_description": "提供技术文档和开发教程"
}
```

### 4. 更新分类

**接口地址**: `PUT /admin/categories/{id}`
**权限要求**: 管理员权限

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 分类ID |

**请求参数**: 同创建分类，所有参数都是可选的

### 5. 删除分类

**接口地址**: `DELETE /admin/categories/{id}`
**权限要求**: 超级管理员权限

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 分类ID |

**注意**: 只能删除没有子分类的分类

### 6. 获取菜单树

**接口地址**: `GET /admin/categories/tree`
**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| only_visible | bool | 否 | 只获取显示的分类，默认true |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "parent_id": 0,
      "name": "新闻资讯",
      "slug": "news",
      "type": "list",
      "link_url": null,
      "level": 1,
      "sort_order": 100,
      "children": [
        {
          "id": 5,
          "parent_id": 1,
          "name": "行业动态",
          "slug": "industry-news",
          "type": "list",
          "link_url": null,
          "level": 2,
          "sort_order": 50,
          "children": []
        }
      ]
    }
  ]
}
```

### 7. 获取子分类

**接口地址**: `GET /admin/categories/children`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| parent_id | int | 否 | 父分类ID，默认0（顶级） |
| only_visible | bool | 否 | 只获取显示的分类，默认false |

### 8. 获取面包屑路径

**接口地址**: `GET /admin/categories/{id}/breadcrumb`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 分类ID |

### 9. 移动分类

**接口地址**: `POST /admin/categories/{id}/move`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 分类ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| new_parent_id | int | 是 | 新父分类ID |

### 10. 切换显示状态

**接口地址**: `POST /admin/categories/{id}/toggle-show`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 分类ID |

### 11. 批量更新排序

**接口地址**: `POST /admin/categories/update-sort`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sort_data | array | 是 | 排序数据数组 |

**请求示例**:
```json
{
  "sort_data": [
    {"id": 1, "sort_order": 100},
    {"id": 2, "sort_order": 90},
    {"id": 3, "sort_order": 80}
  ]
}
```

### 12. 根据类型获取分类

**接口地址**: `GET /admin/categories/type/{type}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 是 | 分类类型（list/single/link） |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| only_visible | bool | 否 | 只获取显示的分类，默认false |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 公开接口

以下接口无需权限验证，可以直接访问：

### 获取公开菜单树

**接口地址**: `GET /categories/public/tree`
**权限要求**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "parent_id": 0,
      "name": "新闻资讯",
      "slug": "news",
      "type": "list",
      "link_url": null,
      "level": 1,
      "sort_order": 100,
      "children": []
    }
  ]
}
```

### 获取公开分类列表

**接口地址**: `GET /categories/public/list`
**权限要求**: 无

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认10 |
| type | string | 否 | 分类类型筛选 |

## 权限级别说明

### 管理员权限接口
- 获取分类列表
- 获取分类详情
- 创建分类
- 更新分类
- 获取菜单树
- 获取子分类
- 获取面包屑路径
- 移动分类
- 批量更新排序
- 切换显示状态
- 根据类型获取分类

### 超级管理员权限接口
- 删除分类

### 公开接口
- 获取公开菜单树
- 获取公开分类列表

## 业务规则

1. **类型约束**: 只有`list`类型的分类可以创建子分类
2. **循环引用**: 不能将分类移动到自己的子分类下
3. **唯一性**: 分类别名（slug）必须全局唯一
4. **删除限制**: 有子分类的分类不能被删除
5. **链接验证**: `link`类型的分类必须设置有效的链接地址
6. **层级限制**: 建议不超过5级分类深度
