# 文章自定义字段管理 API 文档

## 概述

文章自定义字段管理API提供了灵活的字段扩展功能，支持：
- **字段类型**：标签(tag)、文本(text)、长文本(textarea)
- **字段配置**：必填控制、默认值、验证规则
- **动态管理**：运行时添加、修改、删除字段
- **使用统计**：字段使用情况分析

## 权限说明

- **查看权限**：管理员权限（content_admin或super_admin）
- **管理权限**：超级管理员权限（super_admin）

## 基础信息

- **Base URL**: `/admin/article-custom-fields`
- **Content-Type**: `application/json`

## 字段类型说明

### 1. 标签类型 (tag)
- **存储格式**: JSON数组
- **示例值**: `["标签1", "标签2", "标签3"]`
- **用途**: 多标签选择、关键词标记

### 2. 文本类型 (text)
- **存储格式**: 字符串
- **示例值**: `"单行文本内容"`
- **用途**: 简短信息输入

### 3. 长文本类型 (textarea)
- **存储格式**: 字符串
- **示例值**: `"多行文本内容\n支持换行"`
- **用途**: 较长文本输入，不支持富文本

## API接口列表

### 1. 获取字段列表

**接口地址**: `GET /admin/article-custom-fields`
**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认15 |
| name | string | 否 | 字段名称搜索 |
| field_key | string | 否 | 字段键名搜索 |
| field_type | string | 否 | 字段类型筛选 |
| is_required | int | 否 | 必填状态筛选 |
| is_active | int | 否 | 启用状态筛选 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "文章标签",
        "field_key": "article_tags",
        "field_type": "tag",
        "field_type_text": "标签",
        "is_required": 0,
        "required_text": "否",
        "default_value": null,
        "options": {
          "placeholder": "请输入标签",
          "max_tags": 10
        },
        "sort_order": 100,
        "is_active": 1,
        "active_text": "启用",
        "description": "用于标记文章的关键词标签",
        "validation_rules": {
          "max_length": 500
        },
        "create_time": "2024-01-01 10:00:00"
      }
    ],
    "total": 5,
    "page": 1,
    "limit": 15,
    "pages": 1
  }
}
```

### 2. 获取启用的字段

**接口地址**: `GET /admin/article-custom-fields/active`
**权限要求**: 管理员权限

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "文章标签",
      "field_key": "article_tags",
      "field_type": "tag",
      "is_required": 0,
      "default_value": null,
      "options": {...},
      "validation_rules": {...}
    }
  ]
}
```

### 3. 获取字段详情

**接口地址**: `GET /admin/article-custom-fields/{id}`
**权限要求**: 管理员权限

### 4. 创建字段

**接口地址**: `POST /admin/article-custom-fields`
**权限要求**: 超级管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 字段显示名称 |
| field_key | string | 是 | 字段键名，唯一标识 |
| field_type | string | 是 | 字段类型：tag/text/textarea |
| is_required | int | 否 | 是否必填，默认0 |
| default_value | string | 否 | 默认值 |
| options | object | 否 | 字段选项配置 |
| sort_order | int | 否 | 排序权重，默认0 |
| is_active | int | 否 | 是否启用，默认1 |
| description | string | 否 | 字段描述 |
| validation_rules | object | 否 | 验证规则 |

**字段选项配置示例**:
```json
{
  "options": {
    "placeholder": "请输入内容",
    "max_tags": 10,
    "rows": 4
  }
}
```

**验证规则示例**:
```json
{
  "validation_rules": {
    "max_length": 500,
    "pattern": "url"
  }
}
```

**请求示例**:
```json
{
  "name": "文章来源",
  "field_key": "article_source",
  "field_type": "text",
  "is_required": 0,
  "default_value": "",
  "options": {
    "placeholder": "请输入文章来源"
  },
  "sort_order": 90,
  "is_active": 1,
  "description": "记录文章的来源信息",
  "validation_rules": {
    "max_length": 100
  }
}
```

### 5. 更新字段

**接口地址**: `PUT /admin/article-custom-fields/{id}`
**权限要求**: 超级管理员权限

**请求参数**: 与创建字段相同，所有参数均为可选

### 6. 删除字段

**接口地址**: `DELETE /admin/article-custom-fields/{id}`
**权限要求**: 超级管理员权限

**注意**: 只能删除未被使用的字段

### 7. 切换启用状态

**接口地址**: `POST /admin/article-custom-fields/{id}/toggle-active`
**权限要求**: 超级管理员权限

### 8. 根据类型获取字段

**接口地址**: `GET /admin/article-custom-fields/type/{type}`
**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 是 | 字段类型：tag/text/textarea |
| only_active | bool | 否 | 只获取启用字段，默认true |

### 9. 获取字段使用统计

**接口地址**: `GET /admin/article-custom-fields/{id}/usage-stats`
**权限要求**: 管理员权限

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_usage": 50,
    "non_empty_usage": 45
  }
}
```

### 10. 批量更新排序

**接口地址**: `POST /admin/article-custom-fields/update-sort`
**权限要求**: 超级管理员权限

**请求参数**:
```json
{
  "sort_data": [
    {"id": 1, "sort_order": 100},
    {"id": 2, "sort_order": 90}
  ]
}
```

### 11. 获取字段统计

**接口地址**: `GET /admin/article-custom-fields/statistics`
**权限要求**: 管理员权限

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 5,
    "active": 4,
    "inactive": 1,
    "required": 2,
    "tag_type": 2,
    "text_type": 2,
    "textarea_type": 1,
    "articles_with_custom_fields": 30,
    "fields_in_use": 4
  }
}
```

## 字段键名规范

字段键名必须遵循以下规范：
- 以字母开头
- 只能包含字母、数字和下划线
- 建议使用小写字母和下划线
- 具有描述性，如：`article_tags`、`source_url`、`editor_note`

## 验证规则说明

### 通用规则
- `max_length`: 最大字符长度
- `min_length`: 最小字符长度
- `pattern`: 正则表达式模式（如：url、email）

### 标签类型特有规则
- `max_tags`: 最大标签数量
- `min_tags`: 最小标签数量

## 使用示例

### 创建标签类型字段

```javascript
const fieldData = {
  name: "文章标签",
  field_key: "article_tags",
  field_type: "tag",
  is_required: 0,
  options: {
    placeholder: "请输入标签，多个标签用逗号分隔",
    max_tags: 10
  },
  validation_rules: {
    max_length: 500
  },
  description: "用于标记文章的关键词标签"
};

fetch('/admin/article-custom-fields', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token'
  },
  body: JSON.stringify(fieldData)
});
```

### 创建文本类型字段

```javascript
const fieldData = {
  name: "文章来源",
  field_key: "article_source",
  field_type: "text",
  is_required: 0,
  options: {
    placeholder: "请输入文章来源"
  },
  validation_rules: {
    max_length: 100
  },
  description: "记录文章的来源信息"
};
```
