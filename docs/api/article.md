# 文章管理 API 文档

## 概述

文章管理API提供了完整的文章内容管理功能，支持：
- **文章CRUD操作**：创建、读取、更新、删除文章
- **自定义字段支持**：灵活的字段扩展机制
- **状态管理**：草稿、发布、归档状态控制
- **置顶功能**：重要文章置顶显示
- **SEO优化**：完整的SEO字段支持

## 权限说明

本API使用方法级权限控制，不同接口有不同的权限要求：

- **管理员权限**：需要登录的管理员（content_admin或super_admin）
- **超级管理员权限**：需要超级管理员角色（super_admin）

### 认证方式

需要权限的接口需要在请求头中包含认证信息：

```
Authorization: Bearer {token}
```

## 基础信息

- **Base URL**: `/admin/articles`
- **Content-Type**: `application/json`
- **响应格式**: JSON

## 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

## 文章状态说明

- **draft**: 草稿状态，未发布
- **published**: 已发布状态，对外可见
- **archived**: 已归档状态，不对外显示

## API接口列表

### 1. 获取文章列表

**接口地址**: `GET /admin/articles`
**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认15 |
| title | string | 否 | 标题搜索 |
| author | string | 否 | 作者搜索 |
| status | string | 否 | 状态筛选 |
| category_id | int | 否 | 分类ID筛选 |
| is_top | int | 否 | 置顶状态筛选 |
| include_custom_fields | bool | 否 | 是否包含自定义字段，默认false |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "category_id": 1,
        "title": "文章标题",
        "slug": "article-slug",
        "summary": "文章摘要",
        "author": "作者",
        "status": "published",
        "status_text": "已发布",
        "is_top": 0,
        "top_text": "否",
        "view_count": 100,
        "publish_time": "2024-01-01 12:00:00",
        "create_time": "2024-01-01 10:00:00",
        "custom_fields": {
          "article_tags": {
            "field": {...},
            "value": ["标签1", "标签2"],
            "display_value": "标签1, 标签2"
          }
        }
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 15,
    "pages": 4
  }
}
```

### 2. 获取文章详情

**接口地址**: `GET /admin/articles/{id}`
**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 文章ID |
| include_custom_fields | bool | 否 | 是否包含自定义字段，默认true |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "category_id": 1,
    "title": "文章标题",
    "slug": "article-slug",
    "summary": "文章摘要",
    "content": "文章正文内容",
    "author": "作者",
    "cover_image": "/uploads/cover.jpg",
    "status": "published",
    "is_top": 0,
    "sort_order": 100,
    "view_count": 100,
    "seo_title": "SEO标题",
    "seo_keywords": "关键词1,关键词2",
    "seo_description": "SEO描述",
    "publish_time": "2024-01-01 12:00:00",
    "create_time": "2024-01-01 10:00:00",
    "update_time": "2024-01-01 11:00:00",
    "custom_fields": {
      "article_tags": {
        "field": {
          "id": 1,
          "name": "文章标签",
          "field_key": "article_tags",
          "field_type": "tag",
          "is_required": 0
        },
        "value": ["标签1", "标签2"],
        "display_value": "标签1, 标签2"
      },
      "source": {
        "field": {
          "id": 2,
          "name": "来源",
          "field_key": "source",
          "field_type": "text",
          "is_required": 0
        },
        "value": "官方网站",
        "display_value": "官方网站"
      }
    }
  }
}
```

### 3. 创建文章

**接口地址**: `POST /admin/articles`
**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| category_id | int | 是 | 分类ID |
| title | string | 是 | 文章标题 |
| slug | string | 否 | URL别名，为空时自动生成 |
| summary | string | 否 | 文章摘要 |
| content | string | 是 | 文章正文 |
| author | string | 否 | 作者 |
| cover_image | string | 否 | 封面图片 |
| status | string | 否 | 状态，默认draft |
| is_top | int | 否 | 是否置顶，默认0 |
| sort_order | int | 否 | 排序权重，默认0 |
| seo_title | string | 否 | SEO标题 |
| seo_keywords | string | 否 | SEO关键词 |
| seo_description | string | 否 | SEO描述 |
| publish_time | string | 否 | 发布时间 |
| custom_fields | object | 否 | 自定义字段值 |

**自定义字段格式**:
```json
{
  "custom_fields": {
    "article_tags": ["标签1", "标签2"],
    "source": "官方网站",
    "editor_note": "编辑备注信息"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "title": "文章标题",
    ...
  }
}
```

### 4. 更新文章

**接口地址**: `PUT /admin/articles/{id}`
**权限要求**: 管理员权限

**请求参数**: 与创建文章相同，所有参数均为可选

### 5. 删除文章

**接口地址**: `DELETE /admin/articles/{id}`
**权限要求**: 超级管理员权限

### 6. 切换置顶状态

**接口地址**: `POST /admin/articles/{id}/toggle-top`
**权限要求**: 管理员权限

### 7. 切换文章状态

**接口地址**: `POST /admin/articles/{id}/change-status`
**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 是 | 新状态：draft/published/archived |

### 8. 批量更新排序

**接口地址**: `POST /admin/articles/update-sort`
**权限要求**: 管理员权限

**请求参数**:
```json
{
  "sort_data": [
    {"id": 1, "sort_order": 100},
    {"id": 2, "sort_order": 90}
  ]
}
```

### 9. 获取文章统计

**接口地址**: `GET /admin/articles/statistics`
**权限要求**: 管理员权限

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 100,
    "published": 80,
    "draft": 15,
    "archived": 5,
    "top": 10,
    "total_views": 50000
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### 创建带自定义字段的文章

```javascript
const articleData = {
  category_id: 1,
  title: "新文章标题",
  content: "文章内容...",
  author: "张三",
  status: "published",
  custom_fields: {
    article_tags: ["技术", "教程"],
    source: "官方博客",
    editor_note: "这是一篇重要的技术文章"
  }
};

fetch('/admin/articles', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token'
  },
  body: JSON.stringify(articleData)
});
```
