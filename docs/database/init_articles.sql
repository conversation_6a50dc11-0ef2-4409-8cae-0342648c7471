-- 文章模块完整初始化脚本
-- 包含文章表、自定义字段表、字段值表的创建和示例数据

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 创建文章表
-- =============================================
SOURCE articles.sql;

-- =============================================
-- 2. 创建自定义字段定义表
-- =============================================
SOURCE article_custom_fields.sql;

-- =============================================
-- 3. 创建自定义字段值表
-- =============================================
SOURCE article_custom_field_values.sql;

-- =============================================
-- 4. 插入示例文章数据
-- =============================================
INSERT INTO `articles` (`id`, `category_id`, `title`, `slug`, `summary`, `content`, `author`, `cover_image`, `status`, `is_top`, `sort_order`, `view_count`, `seo_title`, `seo_keywords`, `seo_description`, `publish_time`, `create_time`, `update_time`) VALUES
(1, 1, '欢迎使用文章管理系统', 'welcome-to-article-system', '这是一篇介绍文章管理系统功能的示例文章', '# 欢迎使用文章管理系统\n\n这是一个功能强大的文章管理系统，支持：\n\n## 主要功能\n- 文章分类管理\n- 自定义字段支持\n- SEO优化\n- 状态控制\n- 权限管理\n\n## 自定义字段\n系统支持多种类型的自定义字段：\n- 标签类型：支持多标签输入\n- 文本类型：单行文本输入\n- 长文本类型：多行文本输入\n\n感谢您的使用！', '系统管理员', NULL, 'published', 1, 100, 0, '文章管理系统 - 功能介绍', '文章管理,CMS,内容管理', '介绍文章管理系统的主要功能和特性', NOW(), NOW(), NOW()),
(2, 2, 'ThinkPHP开发指南', 'thinkphp-development-guide', 'ThinkPHP框架的开发最佳实践和技巧分享', '# ThinkPHP开发指南\n\n## 简介\nThinkPHP是一个快速、兼容而且简单的轻量级国产PHP开发框架。\n\n## 核心特性\n- MVC架构\n- 路由系统\n- ORM支持\n- 模板引擎\n- 缓存支持\n\n## 开发建议\n1. 遵循PSR规范\n2. 合理使用中间件\n3. 数据验证\n4. 异常处理\n\n更多内容请参考官方文档。', '技术团队', NULL, 'published', 0, 90, 15, 'ThinkPHP开发指南 - 最佳实践', 'ThinkPHP,PHP,开发指南,框架', 'ThinkPHP框架开发的最佳实践和技巧分享', NOW(), NOW(), NOW()),
(3, 1, '系统更新日志', 'system-update-log', '记录系统的重要更新和改进', '# 系统更新日志\n\n## v1.0.0 (2024-01-10)\n### 新增功能\n- 文章管理模块\n- 自定义字段支持\n- 权限控制系统\n\n### 优化改进\n- 提升查询性能\n- 优化用户界面\n- 增强安全性\n\n### 修复问题\n- 修复分页显示问题\n- 解决权限验证bug\n\n感谢大家的反馈和建议！', '开发团队', NULL, 'draft', 0, 80, 3, '系统更新日志 - 版本记录', '更新日志,版本记录,系统更新', '记录系统的重要更新、改进和修复内容', NULL, NOW(), NOW());

-- =============================================
-- 5. 插入示例自定义字段值
-- =============================================
INSERT INTO `article_custom_field_values` (`article_id`, `field_id`, `field_value`, `create_time`, `update_time`) VALUES
-- 第一篇文章的自定义字段值
(1, 1, '["系统介绍", "功能特性", "使用指南"]', NOW(), NOW()),
(1, 2, '官方文档', NOW(), NOW()),
(1, 3, '这是系统的介绍文章，重点突出主要功能特性', NOW(), NOW()),
(1, 5, '重点介绍了文章管理系统的核心功能，包括分类管理、自定义字段、SEO优化等特性，是新用户了解系统的重要参考。', NOW(), NOW()),

-- 第二篇文章的自定义字段值  
(2, 1, '["ThinkPHP", "PHP框架", "开发指南", "最佳实践"]', NOW(), NOW()),
(2, 2, 'ThinkPHP官网', NOW(), NOW()),
(2, 4, 'https://www.thinkphp.cn/', NOW(), NOW()),
(2, 5, '详细介绍了ThinkPHP框架的核心特性和开发建议，适合PHP开发者学习和参考。', NOW(), NOW()),

-- 第三篇文章的自定义字段值
(3, 1, '["更新日志", "版本记录", "系统维护"]', NOW(), NOW()),
(3, 3, '此文章用于记录系统的重要更新，包括新功能、优化改进和问题修复', NOW(), NOW());

-- 重置外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建结果
SELECT '文章模块数据库初始化完成！' as message;
SELECT COUNT(*) as article_count FROM articles;
SELECT COUNT(*) as custom_field_count FROM article_custom_fields;
SELECT COUNT(*) as field_value_count FROM article_custom_field_values;
