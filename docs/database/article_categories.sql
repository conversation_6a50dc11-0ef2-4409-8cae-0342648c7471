-- 文章分类表
-- 支持多级分类，三种类型：list(列表)、single(单章)、link(连接)
-- 只有list类型可以创建子分类，其他类型为最小节点

DROP TABLE IF EXISTS `article_categories`;

CREATE TABLE `article_categories` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '父级ID，0为顶级分类',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `slug` varchar(100) NOT NULL COMMENT '分类别名，用于URL',
  `type` enum('list','single','link') NOT NULL DEFAULT 'list' COMMENT '分类类型：list列表,single单章,link连接',
  `description` text COMMENT '分类描述',
  `link_url` varchar(500) DEFAULT NULL COMMENT '连接类型的URL地址',
  `cover_image` varchar(500) DEFAULT NULL COMMENT '封面图片路径',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重，数字越大越靠前',
  `level` tinyint(3) unsigned NOT NULL DEFAULT 1 COMMENT '层级深度，从1开始',
  `path` varchar(500) DEFAULT NULL COMMENT '层级路径，如：1,2,5',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示：0隐藏，1显示',
  `seo_title` varchar(200) DEFAULT NULL COMMENT 'SEO标题',
  `seo_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
  `seo_description` text COMMENT 'SEO描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_slug` (`slug`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_type` (`type`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_show` (`is_show`),
  KEY `idx_level` (`level`),
  KEY `idx_path` (`path`(255))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章分类表';

-- 添加约束：确保slug的唯一性
-- 添加约束：parent_id不能指向自己
-- 添加约束：single和link类型不能有子分类（通过应用层控制）

-- 创建触发器：自动更新update_time
DELIMITER ;;
CREATE TRIGGER `tr_article_categories_update_time` 
BEFORE UPDATE ON `article_categories` 
FOR EACH ROW 
BEGIN
    SET NEW.update_time = NOW();
END;;
DELIMITER ;
