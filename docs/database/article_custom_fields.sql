-- 文章自定义字段定义表
-- 用于定义文章可以使用的自定义字段，支持多种字段类型和验证规则

DROP TABLE IF EXISTS `article_custom_fields`;

CREATE TABLE `article_custom_fields` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '字段ID',
  `name` varchar(100) NOT NULL COMMENT '字段显示名称',
  `field_key` varchar(100) NOT NULL COMMENT '字段键名，用于程序调用，必须唯一',
  `field_type` enum('tag','text','textarea') NOT NULL DEFAULT 'text' COMMENT '字段类型：tag标签,text文本,textarea长文本',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填：0否，1是',
  `default_value` text COMMENT '默认值',
  `options` json DEFAULT NULL COMMENT '字段选项配置，JSON格式，用于扩展配置',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重，数字越大越靠前',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0禁用，1启用',
  `description` text COMMENT '字段描述说明',
  `validation_rules` json DEFAULT NULL COMMENT '验证规则，JSON格式存储',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_field_key` (`field_key`),
  KEY `idx_field_type` (`field_type`),
  KEY `idx_is_required` (`is_required`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章自定义字段定义表';

-- 创建触发器：自动更新update_time
DELIMITER ;;
CREATE TRIGGER `tr_article_custom_fields_update_time` 
BEFORE UPDATE ON `article_custom_fields` 
FOR EACH ROW 
BEGIN
    SET NEW.update_time = NOW();
END;;
DELIMITER ;

-- 插入示例自定义字段
INSERT INTO `article_custom_fields` (`name`, `field_key`, `field_type`, `is_required`, `default_value`, `options`, `sort_order`, `is_active`, `description`, `validation_rules`, `create_time`, `update_time`) VALUES
('文章标签', 'article_tags', 'tag', 0, NULL, '{"placeholder": "请输入标签，多个标签用逗号分隔", "max_tags": 10}', 100, 1, '用于标记文章的关键词标签', '{"max_length": 500}', NOW(), NOW()),
('来源', 'source', 'text', 0, NULL, '{"placeholder": "请输入文章来源"}', 90, 1, '文章的来源信息', '{"max_length": 100}', NOW(), NOW()),
('编辑备注', 'editor_note', 'textarea', 0, NULL, '{"placeholder": "编辑备注信息", "rows": 3}', 80, 1, '编辑人员的内部备注信息', '{"max_length": 1000}', NOW(), NOW()),
('外部链接', 'external_link', 'text', 0, NULL, '{"placeholder": "https://example.com"}', 70, 1, '相关的外部链接地址', '{"max_length": 500, "pattern": "url"}', NOW(), NOW()),
('重点摘要', 'highlight_summary', 'textarea', 0, NULL, '{"placeholder": "文章重点内容摘要", "rows": 4}', 60, 1, '文章的重点内容摘要，用于特殊展示', '{"max_length": 2000}', NOW(), NOW());
