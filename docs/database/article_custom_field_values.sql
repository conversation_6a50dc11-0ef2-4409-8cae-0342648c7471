-- 文章自定义字段值表
-- 存储每篇文章的自定义字段具体值

DROP TABLE IF EXISTS `article_custom_field_values`;

CREATE TABLE `article_custom_field_values` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `article_id` int(11) unsigned NOT NULL COMMENT '文章ID，关联articles表',
  `field_id` int(11) unsigned NOT NULL COMMENT '字段ID，关联article_custom_fields表',
  `field_value` longtext COMMENT '字段值，支持大文本存储',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_article_field` (`article_id`, `field_id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_field_id` (`field_id`),
  CONSTRAINT `fk_custom_values_article` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_custom_values_field` FOREIGN KEY (`field_id`) REFERENCES `article_custom_fields` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章自定义字段值表';

-- 创建触发器：自动更新update_time
DELIMITER ;;
CREATE TRIGGER `tr_article_custom_field_values_update_time` 
BEFORE UPDATE ON `article_custom_field_values` 
FOR EACH ROW 
BEGIN
    SET NEW.update_time = NOW();
END;;
DELIMITER ;

-- 创建索引优化查询性能
-- 复合索引：根据文章ID和字段ID快速查找
-- 单独索引：支持按字段ID查询所有文章的该字段值

-- 添加字段值搜索的全文索引（可选，用于内容搜索）
-- ALTER TABLE `article_custom_field_values` ADD FULLTEXT KEY `ft_field_value` (`field_value`);
