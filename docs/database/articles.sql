-- 文章表
-- 支持新闻文章的完整管理，包括分类关联、SEO优化、状态控制等功能

DROP TABLE IF EXISTS `articles`;

CREATE TABLE `articles` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `category_id` int(11) unsigned NOT NULL COMMENT '分类ID，关联article_categories表',
  `title` varchar(200) NOT NULL COMMENT '文章标题',
  `slug` varchar(200) NOT NULL COMMENT '文章别名，用于URL',
  `summary` text COMMENT '文章摘要',
  `content` longtext NOT NULL COMMENT '文章正文内容',
  `author` varchar(100) DEFAULT NULL COMMENT '作者',
  `cover_image` varchar(500) DEFAULT NULL COMMENT '封面图片路径',
  `status` enum('draft','published','archived') NOT NULL DEFAULT 'draft' COMMENT '文章状态：draft草稿,published发布,archived归档',
  `is_top` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶：0否，1是',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重，数字越大越靠前',
  `view_count` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '浏览次数',
  `seo_title` varchar(200) DEFAULT NULL COMMENT 'SEO标题',
  `seo_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
  `seo_description` text COMMENT 'SEO描述',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_slug` (`slug`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_top` (`is_top`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_view_count` (`view_count`),
  CONSTRAINT `fk_articles_category` FOREIGN KEY (`category_id`) REFERENCES `article_categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';

-- 创建触发器：自动更新update_time
DELIMITER ;;
CREATE TRIGGER `tr_articles_update_time` 
BEFORE UPDATE ON `articles` 
FOR EACH ROW 
BEGIN
    SET NEW.update_time = NOW();
END;;
DELIMITER ;

-- 创建触发器：发布时自动设置发布时间
DELIMITER ;;
CREATE TRIGGER `tr_articles_publish_time` 
BEFORE UPDATE ON `articles` 
FOR EACH ROW 
BEGIN
    -- 如果状态从非published变为published，且publish_time为空，则设置发布时间
    IF OLD.status != 'published' AND NEW.status = 'published' AND NEW.publish_time IS NULL THEN
        SET NEW.publish_time = NOW();
    END IF;
END;;
DELIMITER ;
