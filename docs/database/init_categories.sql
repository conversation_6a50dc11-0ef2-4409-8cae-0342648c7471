-- 文章分类初始化数据
-- 创建示例分类数据，展示三种类型的使用

-- 清空现有数据（谨慎使用）
-- TRUNCATE TABLE `article_categories`;

-- 插入示例数据
INSERT INTO `article_categories` (`id`, `parent_id`, `name`, `slug`, `type`, `description`, `link_url`, `cover_image`, `sort_order`, `level`, `path`, `is_show`, `seo_title`, `seo_keywords`, `seo_description`, `create_time`, `update_time`) VALUES
-- 顶级分类（列表类型）
(1, 0, '新闻资讯', 'news', 'list', '最新的新闻资讯内容', NULL, NULL, 100, 1, '1', 1, '新闻资讯 - 最新资讯', '新闻,资讯,最新', '提供最新的新闻资讯内容', NOW(), NOW()),
(2, 0, '技术文档', 'tech-docs', 'list', '技术相关的文档和教程', NULL, NULL, 90, 1, '2', 1, '技术文档 - 开发教程', '技术,文档,教程,开发', '提供技术文档和开发教程', NOW(), NOW()),
(3, 0, '关于我们', 'about', 'single', '公司介绍页面', NULL, NULL, 80, 1, '3', 1, '关于我们 - 公司介绍', '关于我们,公司介绍', '了解我们公司的详细信息', NOW(), NOW()),
(4, 0, '外部链接', 'external-links', 'list', '外部链接分类', NULL, NULL, 70, 1, '4', 1, '外部链接', '外部链接', '相关的外部链接资源', NOW(), NOW()),

-- 新闻资讯的子分类
(5, 1, '行业动态', 'industry-news', 'list', '行业相关的动态新闻', NULL, NULL, 50, 2, '1,5', 1, '行业动态 - 最新行业资讯', '行业动态,行业新闻', '关注行业最新动态和发展趋势', NOW(), NOW()),
(6, 1, '公司新闻', 'company-news', 'list', '公司内部新闻动态', NULL, NULL, 40, 2, '1,6', 1, '公司新闻 - 企业动态', '公司新闻,企业动态', '了解公司最新动态和发展情况', NOW(), NOW()),
(7, 1, '产品发布', 'product-release', 'single', '产品发布公告页面', NULL, NULL, 30, 2, '1,7', 1, '产品发布 - 新品公告', '产品发布,新品公告', '最新产品发布信息和公告', NOW(), NOW()),

-- 技术文档的子分类
(8, 2, 'API文档', 'api-docs', 'list', 'API接口文档', NULL, NULL, 50, 2, '2,8', 1, 'API文档 - 接口说明', 'API,接口文档,开发文档', 'API接口的详细使用说明', NOW(), NOW()),
(9, 2, '开发指南', 'dev-guide', 'list', '开发相关的指南和教程', NULL, NULL, 40, 2, '2,9', 1, '开发指南 - 开发教程', '开发指南,开发教程,编程教程', '提供详细的开发指南和教程', NOW(), NOW()),
(10, 2, '常见问题', 'faq', 'single', '常见问题解答页面', NULL, NULL, 30, 2, '2,10', 1, '常见问题 - FAQ', '常见问题,FAQ,帮助', '常见问题的解答和帮助信息', NOW(), NOW()),

-- 外部链接的子分类
(11, 4, '官方网站', 'official-site', 'link', '官方网站链接', 'https://www.example.com', NULL, 50, 2, '4,11', 1, '官方网站', '官方网站', '访问官方网站获取更多信息', NOW(), NOW()),
(12, 4, '合作伙伴', 'partners', 'link', '合作伙伴网站', 'https://partner.example.com', NULL, 40, 2, '4,12', 1, '合作伙伴', '合作伙伴', '我们的合作伙伴网站', NOW(), NOW()),

-- 行业动态的子分类（三级分类）
(13, 5, '技术趋势', 'tech-trends', 'single', '技术发展趋势分析', NULL, NULL, 30, 3, '1,5,13', 1, '技术趋势 - 行业分析', '技术趋势,行业分析', '分析技术发展趋势和行业动向', NOW(), NOW()),
(14, 5, '市场分析', 'market-analysis', 'single', '市场分析报告', NULL, NULL, 20, 3, '1,5,14', 1, '市场分析 - 行业报告', '市场分析,行业报告', '深度市场分析和行业报告', NOW(), NOW()),

-- API文档的子分类
(15, 8, '用户API', 'user-api', 'single', '用户相关API接口', NULL, NULL, 30, 3, '2,8,15', 1, '用户API - 接口文档', '用户API,用户接口', '用户相关的API接口文档', NOW(), NOW()),
(16, 8, '内容API', 'content-api', 'single', '内容相关API接口', NULL, NULL, 20, 3, '2,8,16', 1, '内容API - 接口文档', '内容API,内容接口', '内容管理相关的API接口文档', NOW(), NOW()),

-- 开发指南的子分类
(17, 9, '快速开始', 'quick-start', 'single', '快速开始指南', NULL, NULL, 30, 3, '2,9,17', 1, '快速开始 - 入门指南', '快速开始,入门指南', '帮助开发者快速上手的入门指南', NOW(), NOW()),
(18, 9, '最佳实践', 'best-practices', 'single', '开发最佳实践', NULL, NULL, 20, 3, '2,9,18', 1, '最佳实践 - 开发规范', '最佳实践,开发规范', '推荐的开发最佳实践和规范', NOW(), NOW());

-- 重置自增ID（可选）
-- ALTER TABLE `article_categories` AUTO_INCREMENT = 19;

-- 验证数据插入
SELECT 
    id,
    parent_id,
    name,
    slug,
    type,
    level,
    path,
    is_show
FROM `article_categories` 
ORDER BY level ASC, sort_order DESC, id ASC;
