# 门户前台页面使用指南

## 🎯 页面概述

门户前台页面是一个真实的新闻门户网站首页，完全由后台的门户配置系统驱动。页面会根据管理员在后台配置的模块和设置来动态渲染内容。

## 🌐 访问地址

- **门户首页**: `http://your-domain/portal` 或 `http://your-domain/`
- **管理后台**: `http://your-domain/admin/portal_module.html`

## 🏗️ 页面结构

### 头部区域
- **网站Logo**: 显示配置的网站名称
- **搜索框**: 支持新闻搜索功能
- **导航菜单**: 自动显示已启用的文章分类
- **实时时间**: 显示当前日期时间

### 主体内容
页面主体完全由后台配置的模块组成，支持以下模块类型：

#### 1. 新闻轮播 (news_banner)
- **布局**: 单列
- **功能**: 展示最新5条有封面图的新闻
- **特效**: 自动轮播，淡入淡出效果
- **配置项**:
  ```json
  {
    "auto_play": true,
    "interval": 5000,
    "show_dots": true,
    "height": "400px"
  }
  ```

#### 2. 热门新闻 (hot_news)
- **布局**: 双列
- **功能**: 按浏览量排序显示热门新闻
- **显示**: 新闻标题、发布时间、浏览量、分类
- **配置项**:
  ```json
  {
    "limit": 10,
    "show_image": true,
    "show_date": true
  }
  ```

#### 3. 分类导航 (category_nav)
- **布局**: 单列
- **功能**: 显示所有启用的文章分类
- **样式**: 圆角按钮，悬停变色
- **配置项**:
  ```json
  {
    "style": "horizontal",
    "show_icon": true,
    "max_items": 8
  }
  ```

#### 4. 最新文章 (latest_articles)
- **布局**: 双列
- **功能**: 显示最新发布的文章列表
- **显示**: 文章标题、作者、发布时间、摘要
- **配置项**:
  ```json
  {
    "limit": 12,
    "show_summary": true,
    "summary_length": 100
  }
  ```

#### 5. 侧边广告 (sidebar_ads)
- **布局**: 单列
- **功能**: 显示广告位或推广内容
- **配置项**:
  ```json
  {
    "position": "right",
    "width": "300px",
    "height": "250px"
  }
  ```

### 底部区域
- **友情链接**: 分栏显示相关链接
- **联系信息**: 网站联系方式
- **版权信息**: 自动显示网站名称和年份

## 🎨 响应式设计

### 桌面端
- **单列模块**: 占满整行宽度
- **双列模块**: 并排显示，各占48%宽度

### 移动端
- **自适应**: 所有模块自动变为单列布局
- **触摸优化**: 适配移动设备操作

## 🔧 配置管理

### 基础配置
在 `门户基础配置` 中可以设置：
- `site_name`: 网站名称
- `site_logo`: 网站Logo
- `site_keywords`: SEO关键词
- `site_description`: SEO描述

### 模块配置
在 `门户模块配置` 中可以：
- **启用/禁用模块**: 控制模块是否显示
- **调整排序**: 拖拽改变模块显示顺序
- **设置列数**: 选择1列或2列布局
- **配置参数**: 自定义模块的显示参数

## 📊 数据来源

### 新闻数据
页面自动从以下数据表获取内容：
- `articles`: 文章主表
- `article_categories`: 文章分类表

### 数据筛选
- **轮播新闻**: 有封面图的已发布文章，按发布时间排序
- **热门新闻**: 已发布文章，按浏览量排序
- **最新文章**: 已发布文章，按发布时间排序
- **分类导航**: 已启用的文章分类，按排序权重排序

## 🚀 使用流程

### 1. 初始化数据
```sql
-- 执行门户系统数据库脚本
mysql -u用户名 -p数据库名 < database/portal_system.sql
```

### 2. 配置模块
1. 访问 `/admin/portal_module.html`
2. 启用需要的模块
3. 调整模块排序和布局
4. 配置模块参数

### 3. 发布内容
1. 在后台发布新闻文章
2. 设置文章封面图（用于轮播）
3. 创建文章分类
4. 确保文章状态为"已发布"

### 4. 查看效果
访问 `/portal` 查看门户首页效果

## 🎯 最佳实践

### 模块布局建议
```
1. 新闻轮播 (单列) - 吸引眼球
2. 分类导航 (单列) - 方便导航
3. 热门新闻 (双列) + 侧边广告 (双列) - 内容丰富
4. 最新文章 (双列) + 其他模块 (双列) - 信息展示
```

### 内容管理建议
- **轮播图片**: 建议尺寸 1200×400px
- **文章封面**: 建议尺寸 300×200px
- **文章摘要**: 控制在100字以内
- **分类数量**: 建议不超过10个主分类

### 性能优化
- **图片压缩**: 上传前压缩图片文件
- **缓存设置**: 合理设置配置缓存时间
- **内容分页**: 大量文章时使用分页

## 🔍 故障排除

### 常见问题

1. **页面显示"模块暂未实现"**
   - 检查模块是否已启用
   - 确认模块名称拼写正确

2. **轮播图不显示**
   - 确保文章有封面图
   - 检查图片路径是否正确

3. **分类导航为空**
   - 在后台创建文章分类
   - 确保分类状态为"显示"

4. **新闻列表为空**
   - 发布一些新闻文章
   - 确保文章状态为"已发布"

### 调试方法
1. 检查浏览器控制台错误
2. 查看ThinkPHP日志文件
3. 确认数据库连接正常
4. 验证权限配置

## 📈 扩展开发

### 添加新模块
1. 创建模块模板文件 `app/index/view/modules/模块名.html`
2. 在 `portal.html` 中添加对应的 case 分支
3. 在后台创建对应的模块配置

### 自定义样式
1. 修改 `portal.html` 中的CSS样式
2. 添加自定义JavaScript功能
3. 集成第三方UI组件

---

**版本**: v1.0.0  
**更新时间**: 2025-01-10  
**技术栈**: ThinkPHP 8 + layui + MySQL
