# 文章管理系统使用指南

## 系统概述

文章管理系统是一个功能完整的内容管理模块，支持文章的创建、编辑、发布和管理，同时提供了灵活的自定义字段扩展功能。

## 核心功能

### 1. 文章管理
- **文章CRUD**：完整的增删改查功能
- **状态控制**：草稿、发布、归档三种状态
- **置顶功能**：重要文章可设置置顶
- **分类关联**：与文章分类系统集成
- **SEO优化**：完整的SEO字段支持
- **浏览统计**：文章浏览次数统计

### 2. 自定义字段
- **字段类型**：支持标签、文本、长文本三种类型
- **灵活配置**：可设置必填、默认值、验证规则
- **动态管理**：运行时添加、修改字段定义
- **使用统计**：字段使用情况分析

## 快速开始

### 1. 数据库初始化

执行以下SQL文件来初始化数据库：

```bash
# 进入项目根目录
cd /www/wwwroot/news.test.jijiaox.com

# 执行初始化脚本
mysql -u username -p database_name < docs/database/init_articles.sql
```

或者分别执行：
```sql
SOURCE docs/database/articles.sql;
SOURCE docs/database/article_custom_fields.sql;
SOURCE docs/database/article_custom_field_values.sql;
```

### 2. 验证安装

访问以下接口验证系统是否正常工作：

```bash
# 获取文章列表（需要认证）
curl -H "Authorization: Bearer your-token" \
     http://your-domain/admin/articles

# 获取自定义字段列表
curl -H "Authorization: Bearer your-token" \
     http://your-domain/admin/article-custom-fields/active
```

## 使用流程

### 1. 配置自定义字段

在创建文章之前，建议先配置所需的自定义字段：

```javascript
// 创建标签字段
const tagField = {
  name: "文章标签",
  field_key: "article_tags",
  field_type: "tag",
  is_required: 0,
  options: {
    placeholder: "请输入标签",
    max_tags: 10
  },
  description: "用于标记文章的关键词"
};

// 创建来源字段
const sourceField = {
  name: "文章来源",
  field_key: "source",
  field_type: "text",
  is_required: 0,
  options: {
    placeholder: "请输入来源"
  },
  validation_rules: {
    max_length: 100
  }
};
```

### 2. 创建文章

创建文章时可以同时设置自定义字段值：

```javascript
const articleData = {
  category_id: 1,
  title: "文章标题",
  content: "文章内容...",
  author: "作者姓名",
  status: "published",
  custom_fields: {
    article_tags: ["技术", "教程", "开发"],
    source: "官方博客",
    editor_note: "这是一篇重要文章"
  }
};
```

### 3. 文章状态管理

文章支持三种状态：

- **draft（草稿）**：文章编辑中，不对外显示
- **published（已发布）**：文章已发布，对外可见
- **archived（已归档）**：文章已归档，不对外显示

状态切换：
```javascript
// 发布文章
fetch(`/admin/articles/${articleId}/change-status`, {
  method: 'POST',
  body: JSON.stringify({ status: 'published' })
});

// 设置置顶
fetch(`/admin/articles/${articleId}/toggle-top`, {
  method: 'POST'
});
```

## 权限配置

### 权限级别

1. **管理员权限（content_admin）**
   - 查看文章列表和详情
   - 创建、编辑文章
   - 切换文章状态和置顶
   - 查看自定义字段

2. **超级管理员权限（super_admin）**
   - 所有管理员权限
   - 删除文章
   - 管理自定义字段（增删改）

### 权限验证

系统使用注解方式进行权限控制：

```php
#[RequireRole(message: '查看文章列表需要管理员权限')]
public function index(Request $request): Response

#[RequireRole(roles: ['super_admin'], message: '删除文章需要超级管理员权限')]
public function delete(Request $request): Response
```

## 自定义字段详解

### 字段类型

#### 1. 标签类型 (tag)
```json
{
  "field_type": "tag",
  "options": {
    "placeholder": "请输入标签",
    "max_tags": 10
  },
  "validation_rules": {
    "max_length": 500
  }
}
```

**存储格式**: JSON数组
**使用场景**: 文章标签、关键词、分类标记

#### 2. 文本类型 (text)
```json
{
  "field_type": "text",
  "options": {
    "placeholder": "请输入内容"
  },
  "validation_rules": {
    "max_length": 255
  }
}
```

**存储格式**: 字符串
**使用场景**: 来源、作者备注、外部链接

#### 3. 长文本类型 (textarea)
```json
{
  "field_type": "textarea",
  "options": {
    "placeholder": "请输入内容",
    "rows": 4
  },
  "validation_rules": {
    "max_length": 2000
  }
}
```

**存储格式**: 字符串
**使用场景**: 编辑备注、详细说明、摘要补充

### 字段配置选项

#### options 配置
- `placeholder`: 输入提示文本
- `max_tags`: 标签类型的最大标签数
- `rows`: 长文本类型的行数

#### validation_rules 配置
- `max_length`: 最大字符长度
- `min_length`: 最小字符长度
- `pattern`: 验证模式（如：url、email）

## 性能优化建议

### 1. 数据库优化
- 为常用查询字段添加索引
- 定期清理无用的字段值记录
- 考虑对大量数据进行分表

### 2. 查询优化
- 列表页面可选择不加载自定义字段
- 使用缓存存储字段定义
- 批量操作时使用事务

### 3. 字段设计
- 合理设计字段类型和验证规则
- 避免创建过多不必要的字段
- 定期清理未使用的字段定义

## 扩展开发

### 添加新字段类型

1. 在 `ArticleCustomFieldBean` 中添加新类型常量
2. 更新字段验证逻辑
3. 在前端添加对应的输入组件
4. 更新API文档

### 集成其他模块

系统设计为模块化架构，可以轻松集成：
- 用户系统（文章作者关联）
- 评论系统（文章评论功能）
- 搜索系统（全文搜索支持）
- 缓存系统（性能优化）

## 故障排除

### 常见问题

1. **字段值保存失败**
   - 检查字段类型是否正确
   - 验证字段值格式
   - 确认字段是否启用

2. **权限验证失败**
   - 检查用户角色配置
   - 确认认证token有效性
   - 验证中间件配置

3. **数据库连接问题**
   - 检查数据库配置
   - 确认表结构是否正确
   - 验证外键约束

### 调试技巧

1. 开启调试模式查看详细错误信息
2. 检查日志文件定位问题
3. 使用API测试工具验证接口
4. 查看数据库查询日志

## 更新日志

### v1.0.0 (2024-01-10)
- 初始版本发布
- 完整的文章管理功能
- 自定义字段支持
- RESTful API接口
- 权限控制系统
- 完整的文档和示例
