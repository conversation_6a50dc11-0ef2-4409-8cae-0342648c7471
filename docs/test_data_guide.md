# 门户系统测试数据使用指南

## 📋 数据概述

本测试数据包含了完整的新闻门户网站内容，包括文章分类和文章内容，让门户页面立即显示出专业的效果。

## 🗂️ 数据内容

### 文章分类（11个）
1. **时政新闻** - 国内外重要时政新闻
2. **科技资讯** - 最新科技动态和创新资讯
   - 人工智能 - AI技术和应用资讯
   - 互联网 - 互联网行业动态
   - 手机数码 - 手机和数码产品资讯
3. **体育新闻** - 体育赛事和运动资讯
   - 足球 - 足球赛事和新闻
   - 篮球 - 篮球赛事和新闻
4. **娱乐八卦** - 娱乐圈动态和明星资讯
5. **财经资讯** - 财经新闻和投资理财
6. **社会新闻** - 社会热点和民生新闻

### 文章内容（12篇）
- **时政类**：全国两会、国务院会议等
- **科技类**：ChatGPT-5、互联网趋势、iPhone 17等
- **体育类**：足球改革、NBA全明星等
- **娱乐类**：春节档电影、导演新作等
- **财经类**：央行降准、新能源汽车等
- **社会类**：降雪天气等

## 🚀 安装步骤

### 1. 导入测试数据

```bash
# 进入项目根目录
cd /www/wwwroot/news.test.jijiaox.com

# 导入测试数据
mysql -u用户名 -p数据库名 < database/portal_test_data.sql
```

### 2. 验证数据导入

```sql
-- 检查分类数据
SELECT COUNT(*) as category_count FROM article_categories;

-- 检查文章数据
SELECT COUNT(*) as article_count FROM articles;

-- 查看分类列表
SELECT id, name, type, level FROM article_categories ORDER BY level, sort_order DESC;

-- 查看文章列表
SELECT id, title, category_id, status, view_count FROM articles ORDER BY publish_time DESC;
```

### 3. 访问门户页面

直接访问网站根目录：`http://your-domain/`

## 📊 数据特点

### 真实性
- 所有文章标题和内容都具有真实感
- 涵盖了新闻网站的主要分类
- 包含了不同类型的新闻内容

### 完整性
- 每篇文章都有完整的标题、摘要、内容
- 包含封面图片URL（用于轮播展示）
- 设置了不同的浏览量（用于热门排序）
- 配置了发布时间（用于最新排序）

### 多样性
- 包含置顶文章和普通文章
- 不同分类的文章数量分布合理
- 浏览量分布从几百到几千不等

## 🎨 展示效果

### 轮播模块
- 显示5篇有封面图的最新文章
- 包含文章标题和摘要
- 自动轮播效果

### 热门新闻
- 按浏览量排序显示10篇文章
- 显示文章图片、标题、发布时间、分类
- 双列布局展示

### 最新文章
- 按发布时间排序显示12篇文章
- 包含作者信息和文章摘要
- 双列布局展示

### 分类导航
- 显示所有启用的文章分类
- 美观的按钮样式
- 支持点击跳转

## 🔧 自定义配置

### 修改浏览量
```sql
-- 增加某篇文章的浏览量
UPDATE articles SET view_count = 5000 WHERE id = 1;
```

### 添加更多文章
```sql
-- 插入新文章
INSERT INTO articles (category_id, title, slug, summary, content, author, cover_image, status, view_count, publish_time, create_time, update_time) 
VALUES (1, '新文章标题', 'new-article-slug', '文章摘要', '文章内容', '作者', 'https://picsum.photos/800/400?random=13', 'published', 100, NOW(), NOW(), NOW());
```

### 修改分类显示
```sql
-- 隐藏某个分类
UPDATE article_categories SET is_show = 0 WHERE id = 4;

-- 调整分类排序
UPDATE article_categories SET sort_order = 110 WHERE id = 2;
```

## 📱 图片说明

测试数据中使用的图片URL格式：
- `https://picsum.photos/800/400?random=数字`
- 这是一个免费的随机图片服务
- 每个数字对应一张固定的图片
- 尺寸为800x400像素，适合新闻展示

如需使用真实图片，请：
1. 将图片上传到 `public/static/images/` 目录
2. 修改数据库中的 `cover_image` 字段
3. 使用相对路径如：`/static/images/news1.jpg`

## 🎯 最佳实践

### 内容管理
- 定期更新文章内容保持新鲜感
- 合理设置文章的浏览量和发布时间
- 确保轮播文章都有高质量的封面图

### 分类管理
- 保持分类结构清晰简洁
- 合理设置分类的排序权重
- 及时隐藏不需要的分类

### 性能优化
- 定期清理过期的文章数据
- 为常用查询字段添加索引
- 考虑使用缓存提升访问速度

## 🔍 故障排除

### 常见问题

1. **轮播图不显示**
   - 检查文章是否有 `cover_image` 字段
   - 确认图片URL是否可访问
   - 验证文章状态是否为 `published`

2. **分类导航为空**
   - 检查 `article_categories` 表是否有数据
   - 确认分类的 `is_show` 字段为1
   - 验证分类的 `type` 字段

3. **新闻列表为空**
   - 检查 `articles` 表是否有数据
   - 确认文章状态为 `published`
   - 验证 `publish_time` 字段不为空

### 调试方法
```sql
-- 检查发布状态的文章
SELECT COUNT(*) FROM articles WHERE status = 'published';

-- 检查有封面图的文章
SELECT COUNT(*) FROM articles WHERE cover_image IS NOT NULL AND cover_image != '';

-- 检查显示状态的分类
SELECT COUNT(*) FROM article_categories WHERE is_show = 1;
```

---

**版本**: v1.0.0  
**更新时间**: 2025-01-10  
**数据量**: 11个分类，12篇文章
