<?php
declare(strict_types=1);

namespace test;

/**
 * 文章分类API测试类
 * 
 * 使用curl模拟HTTP请求测试API接口
 */
class ArticleCategoryApiTest
{
    private string $baseUrl;
    private string $token;
    private array $testIds = [];
    
    public function __construct(string $baseUrl = 'http://localhost', string $token = '')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->token = $token;
    }
    
    /**
     * 运行所有API测试
     */
    public function runAllTests(): void
    {
        echo "=== 文章分类API测试开始 ===\n\n";
        echo "测试地址: {$this->baseUrl}\n";
        echo "认证Token: " . ($this->token ? '已设置' : '未设置') . "\n\n";
        
        try {
            $this->testCreateCategory();
            $this->testGetCategoryList();
            $this->testGetCategoryDetail();
            $this->testUpdateCategory();
            $this->testGetMenuTree();
            $this->testGetChildren();
            $this->testGetBreadcrumb();
            $this->testMoveCategory();
            $this->testToggleShow();
            $this->testGetByType();
            $this->testUpdateSort();
            $this->testDeleteCategory();
            
            echo "\n=== 所有API测试通过！ ===\n";
        } catch (\Exception $e) {
            echo "\n=== API测试失败：" . $e->getMessage() . " ===\n";
        }
    }
    
    /**
     * 测试创建分类
     */
    private function testCreateCategory(): void
    {
        echo "测试创建分类API...\n";
        
        $data = [
            'parent_id' => 0,
            'name' => 'API测试分类',
            'slug' => 'api-test-category',
            'type' => 'list',
            'description' => '这是一个API测试分类',
            'sort_order' => 100,
            'is_show' => 1,
            'seo_title' => 'API测试分类SEO标题'
        ];
        
        $response = $this->post('/admin/categories', $data);
        
        if ($response['code'] === 200 && isset($response['data']['id'])) {
            $this->testIds[] = $response['data']['id'];
            echo "✓ 创建分类成功，ID: {$response['data']['id']}\n";
        } else {
            throw new \Exception("创建分类失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试获取分类列表
     */
    private function testGetCategoryList(): void
    {
        echo "测试获取分类列表API...\n";
        
        $response = $this->get('/admin/categories?page=1&limit=10');
        
        if ($response['code'] === 200 && isset($response['data']['list'])) {
            echo "✓ 获取分类列表成功，总数: {$response['data']['total']}\n";
        } else {
            throw new \Exception("获取分类列表失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试获取分类详情
     */
    private function testGetCategoryDetail(): void
    {
        echo "测试获取分类详情API...\n";
        
        if (empty($this->testIds)) {
            echo "! 跳过分类详情测试（没有可用的分类ID）\n";
            return;
        }
        
        $id = $this->testIds[0];
        $response = $this->get("/admin/categories/{$id}");
        
        if ($response['code'] === 200 && isset($response['data']['id'])) {
            echo "✓ 获取分类详情成功，名称: {$response['data']['name']}\n";
        } else {
            throw new \Exception("获取分类详情失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试更新分类
     */
    private function testUpdateCategory(): void
    {
        echo "测试更新分类API...\n";
        
        if (empty($this->testIds)) {
            echo "! 跳过更新分类测试（没有可用的分类ID）\n";
            return;
        }
        
        $id = $this->testIds[0];
        $data = [
            'name' => 'API测试分类（已更新）',
            'description' => '这是一个更新后的API测试分类'
        ];
        
        $response = $this->put("/admin/categories/{$id}", $data);
        
        if ($response['code'] === 200) {
            echo "✓ 更新分类成功\n";
        } else {
            throw new \Exception("更新分类失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试获取菜单树
     */
    private function testGetMenuTree(): void
    {
        echo "测试获取菜单树API...\n";
        
        $response = $this->get('/admin/categories/tree');
        
        if ($response['code'] === 200 && is_array($response['data'])) {
            echo "✓ 获取菜单树成功，顶级分类数: " . count($response['data']) . "\n";
        } else {
            throw new \Exception("获取菜单树失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试获取子分类
     */
    private function testGetChildren(): void
    {
        echo "测试获取子分类API...\n";
        
        $response = $this->get('/admin/categories/children?parent_id=0');
        
        if ($response['code'] === 200 && is_array($response['data'])) {
            echo "✓ 获取子分类成功，数量: " . count($response['data']) . "\n";
        } else {
            throw new \Exception("获取子分类失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试获取面包屑路径
     */
    private function testGetBreadcrumb(): void
    {
        echo "测试获取面包屑路径API...\n";
        
        if (empty($this->testIds)) {
            echo "! 跳过面包屑测试（没有可用的分类ID）\n";
            return;
        }
        
        $id = $this->testIds[0];
        $response = $this->get("/admin/categories/{$id}/breadcrumb");
        
        if ($response['code'] === 200 && is_array($response['data'])) {
            echo "✓ 获取面包屑路径成功，层级数: " . count($response['data']) . "\n";
        } else {
            throw new \Exception("获取面包屑路径失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试移动分类
     */
    private function testMoveCategory(): void
    {
        echo "测试移动分类API...\n";
        
        if (empty($this->testIds)) {
            echo "! 跳过移动分类测试（没有可用的分类ID）\n";
            return;
        }
        
        $id = $this->testIds[0];
        $data = ['new_parent_id' => 0];
        
        $response = $this->post("/admin/categories/{$id}/move", $data);
        
        if ($response['code'] === 200) {
            echo "✓ 移动分类成功\n";
        } else {
            throw new \Exception("移动分类失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试切换显示状态
     */
    private function testToggleShow(): void
    {
        echo "测试切换显示状态API...\n";
        
        if (empty($this->testIds)) {
            echo "! 跳过切换状态测试（没有可用的分类ID）\n";
            return;
        }
        
        $id = $this->testIds[0];
        $response = $this->post("/admin/categories/{$id}/toggle-show");
        
        if ($response['code'] === 200) {
            echo "✓ 切换显示状态成功\n";
        } else {
            throw new \Exception("切换显示状态失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试根据类型获取分类
     */
    private function testGetByType(): void
    {
        echo "测试根据类型获取分类API...\n";
        
        $response = $this->get('/admin/categories/type/list');
        
        if ($response['code'] === 200 && is_array($response['data'])) {
            echo "✓ 根据类型获取分类成功，数量: " . count($response['data']) . "\n";
        } else {
            throw new \Exception("根据类型获取分类失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试批量更新排序
     */
    private function testUpdateSort(): void
    {
        echo "测试批量更新排序API...\n";
        
        if (empty($this->testIds)) {
            echo "! 跳过排序测试（没有可用的分类ID）\n";
            return;
        }
        
        $data = [
            'sort_data' => [
                ['id' => $this->testIds[0], 'sort_order' => 200]
            ]
        ];
        
        $response = $this->post('/admin/categories/update-sort', $data);
        
        if ($response['code'] === 200) {
            echo "✓ 批量更新排序成功\n";
        } else {
            throw new \Exception("批量更新排序失败: " . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 测试删除分类
     */
    private function testDeleteCategory(): void
    {
        echo "测试删除分类API...\n";

        if (empty($this->testIds)) {
            echo "! 跳过删除分类测试（没有可用的分类ID）\n";
            return;
        }

        foreach ($this->testIds as $id) {
            $response = $this->delete("/admin/categories/{$id}");

            if ($response['code'] === 200) {
                echo "✓ 删除分类成功，ID: {$id}\n";
            } else {
                echo "! 删除分类失败，ID: {$id}，错误: " . ($response['message'] ?? '未知错误') . "\n";
            }
        }
    }

    /**
     * 发送GET请求
     */
    private function get(string $url): array
    {
        return $this->request('GET', $url);
    }

    /**
     * 发送POST请求
     */
    private function post(string $url, array $data = []): array
    {
        return $this->request('POST', $url, $data);
    }

    /**
     * 发送PUT请求
     */
    private function put(string $url, array $data = []): array
    {
        return $this->request('PUT', $url, $data);
    }

    /**
     * 发送DELETE请求
     */
    private function delete(string $url): array
    {
        return $this->request('DELETE', $url);
    }

    /**
     * 发送HTTP请求
     */
    private function request(string $method, string $url, array $data = []): array
    {
        $fullUrl = $this->baseUrl . $url;

        $ch = curl_init();

        // 基础配置
        curl_setopt_array($ch, [
            CURLOPT_URL => $fullUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $this->getHeaders(),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ]);

        // 如果有数据，设置请求体
        if (!empty($data)) {
            if (in_array($method, ['POST', 'PUT'])) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        $response = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
            throw new \Exception("CURL错误: {$error}");
        }

        $result = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("JSON解析错误: " . json_last_error_msg() . "\n响应内容: {$response}");
        }

        return $result;
    }

    /**
     * 获取请求头
     */
    private function getHeaders(): array
    {
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json',
        ];

        if ($this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }

        return $headers;
    }

    /**
     * 设置认证Token
     */
    public function setToken(string $token): void
    {
        $this->token = $token;
    }

    /**
     * 设置基础URL
     */
    public function setBaseUrl(string $baseUrl): void
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }
}

// 如果直接运行此文件，执行测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    // 配置测试参数
    $baseUrl = 'http://localhost'; // 修改为实际的API地址
    $token = ''; // 如果需要认证，设置Token

    $test = new ArticleCategoryApiTest($baseUrl, $token);

    echo "请确保：\n";
    echo "1. 数据库已创建并执行了article_categories.sql\n";
    echo "2. API服务正在运行\n";
    echo "3. 如果需要认证，请设置正确的Token\n\n";
    echo "按回车键继续测试...";
    fgets(STDIN);

    $test->runAllTests();
}
