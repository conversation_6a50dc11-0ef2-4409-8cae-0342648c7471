<?php
/**
 * 文章管理系统测试脚本
 * 
 * 使用方法：
 * 1. 确保数据库已初始化
 * 2. 配置正确的数据库连接
 * 3. 在浏览器中访问此脚本：http://your-domain/docs/test/article_system_test.php
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架（根据实际路径调整）
require_once '../../vendor/autoload.php';

// 测试结果存储
$testResults = [];

/**
 * 添加测试结果
 */
function addTestResult($testName, $success, $message = '', $data = null) {
    global $testResults;
    $testResults[] = [
        'test' => $testName,
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'time' => date('Y-m-d H:i:s')
    ];
}

/**
 * 测试数据库连接
 */
function testDatabaseConnection() {
    try {
        // 这里需要根据实际配置调整数据库连接
        $pdo = new PDO('mysql:host=localhost;dbname=your_database', 'username', 'password');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 检查文章表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'articles'");
        $articlesExists = $stmt->rowCount() > 0;
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'article_custom_fields'");
        $fieldsExists = $stmt->rowCount() > 0;
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'article_custom_field_values'");
        $valuesExists = $stmt->rowCount() > 0;
        
        if ($articlesExists && $fieldsExists && $valuesExists) {
            addTestResult('数据库连接测试', true, '所有必需的表都存在');
        } else {
            addTestResult('数据库连接测试', false, '缺少必需的数据库表');
        }
        
        return $pdo;
    } catch (Exception $e) {
        addTestResult('数据库连接测试', false, '数据库连接失败: ' . $e->getMessage());
        return null;
    }
}

/**
 * 测试Bean类
 */
function testBeanClasses() {
    try {
        // 测试ArticleBean
        if (class_exists('app\api\bean\ArticleBean')) {
            $article = new app\api\bean\ArticleBean();
            $article->title = '测试文章';
            $article->content = '测试内容';
            $article->status = 'draft';
            
            $isValid = $article->validateStatus();
            addTestResult('ArticleBean测试', $isValid, 'ArticleBean类功能正常');
        } else {
            addTestResult('ArticleBean测试', false, 'ArticleBean类不存在');
        }
        
        // 测试ArticleCustomFieldBean
        if (class_exists('app\api\bean\ArticleCustomFieldBean')) {
            $field = new app\api\bean\ArticleCustomFieldBean();
            $field->name = '测试字段';
            $field->fieldKey = 'test_field';
            $field->fieldType = 'text';
            
            $isValid = $field->validateFieldType();
            addTestResult('ArticleCustomFieldBean测试', $isValid, 'ArticleCustomFieldBean类功能正常');
        } else {
            addTestResult('ArticleCustomFieldBean测试', false, 'ArticleCustomFieldBean类不存在');
        }
        
        // 测试ArticleCustomFieldValueBean
        if (class_exists('app\api\bean\ArticleCustomFieldValueBean')) {
            $value = new app\api\bean\ArticleCustomFieldValueBean();
            $value->articleId = 1;
            $value->fieldId = 1;
            $value->fieldValue = '测试值';
            
            $isEmpty = $value->isEmpty();
            addTestResult('ArticleCustomFieldValueBean测试', !$isEmpty, 'ArticleCustomFieldValueBean类功能正常');
        } else {
            addTestResult('ArticleCustomFieldValueBean测试', false, 'ArticleCustomFieldValueBean类不存在');
        }
        
    } catch (Exception $e) {
        addTestResult('Bean类测试', false, 'Bean类测试失败: ' . $e->getMessage());
    }
}

/**
 * 测试Repository类
 */
function testRepositoryClasses() {
    try {
        // 测试ArticleRepository
        if (class_exists('app\api\repository\ArticleRepository')) {
            $repo = new app\api\repository\ArticleRepository();
            addTestResult('ArticleRepository测试', true, 'ArticleRepository类存在');
        } else {
            addTestResult('ArticleRepository测试', false, 'ArticleRepository类不存在');
        }
        
        // 测试ArticleCustomFieldRepository
        if (class_exists('app\api\repository\ArticleCustomFieldRepository')) {
            $repo = new app\api\repository\ArticleCustomFieldRepository();
            addTestResult('ArticleCustomFieldRepository测试', true, 'ArticleCustomFieldRepository类存在');
        } else {
            addTestResult('ArticleCustomFieldRepository测试', false, 'ArticleCustomFieldRepository类不存在');
        }
        
        // 测试ArticleCustomFieldValueRepository
        if (class_exists('app\api\repository\ArticleCustomFieldValueRepository')) {
            $repo = new app\api\repository\ArticleCustomFieldValueRepository();
            addTestResult('ArticleCustomFieldValueRepository测试', true, 'ArticleCustomFieldValueRepository类存在');
        } else {
            addTestResult('ArticleCustomFieldValueRepository测试', false, 'ArticleCustomFieldValueRepository类不存在');
        }
        
    } catch (Exception $e) {
        addTestResult('Repository类测试', false, 'Repository类测试失败: ' . $e->getMessage());
    }
}

/**
 * 测试Service类
 */
function testServiceClasses() {
    try {
        // 测试ArticleService
        if (class_exists('app\api\service\ArticleService')) {
            $service = new app\api\service\ArticleService();
            addTestResult('ArticleService测试', true, 'ArticleService类存在');
        } else {
            addTestResult('ArticleService测试', false, 'ArticleService类不存在');
        }
        
        // 测试ArticleCustomFieldService
        if (class_exists('app\api\service\ArticleCustomFieldService')) {
            $service = new app\api\service\ArticleCustomFieldService();
            addTestResult('ArticleCustomFieldService测试', true, 'ArticleCustomFieldService类存在');
        } else {
            addTestResult('ArticleCustomFieldService测试', false, 'ArticleCustomFieldService类不存在');
        }
        
    } catch (Exception $e) {
        addTestResult('Service类测试', false, 'Service类测试失败: ' . $e->getMessage());
    }
}

/**
 * 测试Controller类
 */
function testControllerClasses() {
    try {
        // 测试ArticleController
        if (class_exists('app\api\controller\admin\ArticleController')) {
            addTestResult('ArticleController测试', true, 'ArticleController类存在');
        } else {
            addTestResult('ArticleController测试', false, 'ArticleController类不存在');
        }
        
        // 测试ArticleCustomFieldController
        if (class_exists('app\api\controller\admin\ArticleCustomFieldController')) {
            addTestResult('ArticleCustomFieldController测试', true, 'ArticleCustomFieldController类存在');
        } else {
            addTestResult('ArticleCustomFieldController测试', false, 'ArticleCustomFieldController类不存在');
        }
        
    } catch (Exception $e) {
        addTestResult('Controller类测试', false, 'Controller类测试失败: ' . $e->getMessage());
    }
}

/**
 * 测试数据库数据
 */
function testDatabaseData($pdo) {
    if (!$pdo) {
        addTestResult('数据库数据测试', false, '数据库连接不可用');
        return;
    }
    
    try {
        // 检查自定义字段数据
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM article_custom_fields");
        $fieldCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($fieldCount > 0) {
            addTestResult('自定义字段数据测试', true, "找到 {$fieldCount} 个自定义字段");
        } else {
            addTestResult('自定义字段数据测试', false, '没有找到自定义字段数据');
        }
        
        // 检查示例文章数据
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM articles");
        $articleCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($articleCount > 0) {
            addTestResult('文章数据测试', true, "找到 {$articleCount} 篇文章");
        } else {
            addTestResult('文章数据测试', false, '没有找到文章数据');
        }
        
        // 检查字段值数据
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM article_custom_field_values");
        $valueCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($valueCount > 0) {
            addTestResult('字段值数据测试', true, "找到 {$valueCount} 个字段值");
        } else {
            addTestResult('字段值数据测试', false, '没有找到字段值数据');
        }
        
    } catch (Exception $e) {
        addTestResult('数据库数据测试', false, '数据库数据测试失败: ' . $e->getMessage());
    }
}

/**
 * 测试文件结构
 */
function testFileStructure() {
    $requiredFiles = [
        '../../app/api/bean/ArticleBean.php',
        '../../app/api/bean/ArticleCustomFieldBean.php',
        '../../app/api/bean/ArticleCustomFieldValueBean.php',
        '../../app/api/repository/ArticleRepository.php',
        '../../app/api/repository/ArticleCustomFieldRepository.php',
        '../../app/api/repository/ArticleCustomFieldValueRepository.php',
        '../../app/api/service/ArticleService.php',
        '../../app/api/service/ArticleCustomFieldService.php',
        '../../app/api/controller/admin/ArticleController.php',
        '../../app/api/controller/admin/ArticleCustomFieldController.php',
        '../database/articles.sql',
        '../database/article_custom_fields.sql',
        '../database/article_custom_field_values.sql',
        '../database/init_articles.sql',
        '../api/article.md',
        '../api/article_custom_field.md',
        '../article_system_guide.md'
    ];
    
    $missingFiles = [];
    foreach ($requiredFiles as $file) {
        if (!file_exists($file)) {
            $missingFiles[] = $file;
        }
    }
    
    if (empty($missingFiles)) {
        addTestResult('文件结构测试', true, '所有必需文件都存在');
    } else {
        addTestResult('文件结构测试', false, '缺少文件: ' . implode(', ', $missingFiles));
    }
}

// 执行测试
echo "<h1>文章管理系统测试报告</h1>";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>";

// 执行各项测试
testFileStructure();
$pdo = testDatabaseConnection();
testBeanClasses();
testRepositoryClasses();
testServiceClasses();
testControllerClasses();
testDatabaseData($pdo);

// 显示测试结果
echo "<h2>测试结果</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f0f0f0;'>";
echo "<th>测试项目</th><th>结果</th><th>说明</th><th>时间</th>";
echo "</tr>";

$successCount = 0;
$totalCount = count($testResults);

foreach ($testResults as $result) {
    $bgColor = $result['success'] ? '#d4edda' : '#f8d7da';
    $textColor = $result['success'] ? '#155724' : '#721c24';
    $status = $result['success'] ? '✓ 通过' : '✗ 失败';
    
    if ($result['success']) {
        $successCount++;
    }
    
    echo "<tr style='background-color: {$bgColor}; color: {$textColor};'>";
    echo "<td>{$result['test']}</td>";
    echo "<td>{$status}</td>";
    echo "<td>{$result['message']}</td>";
    echo "<td>{$result['time']}</td>";
    echo "</tr>";
}

echo "</table>";

// 显示总结
echo "<h2>测试总结</h2>";
echo "<p>总测试项目: {$totalCount}</p>";
echo "<p>通过项目: {$successCount}</p>";
echo "<p>失败项目: " . ($totalCount - $successCount) . "</p>";
echo "<p>成功率: " . round(($successCount / $totalCount) * 100, 2) . "%</p>";

if ($successCount === $totalCount) {
    echo "<div style='background-color: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>🎉 恭喜！所有测试都通过了，文章管理系统已成功部署！</strong>";
    echo "</div>";
} else {
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>⚠️ 部分测试失败，请检查上述错误信息并修复相关问题。</strong>";
    echo "</div>";
}

echo "<h2>下一步操作</h2>";
echo "<ul>";
echo "<li>如果所有测试通过，可以开始使用文章管理系统</li>";
echo "<li>访问 <code>/admin/articles</code> 查看文章列表</li>";
echo "<li>访问 <code>/admin/article-custom-fields</code> 管理自定义字段</li>";
echo "<li>查看 <code>docs/article_system_guide.md</code> 了解详细使用方法</li>";
echo "<li>查看 <code>docs/api/</code> 目录下的API文档</li>";
echo "</ul>";
?>
