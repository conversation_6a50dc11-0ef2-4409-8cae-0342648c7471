<?php
declare(strict_types=1);

namespace test;

use app\api\bean\ArticleCategoryBean;
use app\api\service\ArticleCategoryService;

/**
 * 文章分类Service测试类
 */
class ArticleCategoryServiceTest
{
    private ArticleCategoryService $service;
    private array $testIds = [];
    
    public function __construct()
    {
        $this->service = new ArticleCategoryService();
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests(): void
    {
        echo "=== 文章分类Service测试开始 ===\n\n";
        
        try {
            $this->testCreateCategory();
            $this->testCreateChildCategory();
            $this->testUpdateCategory();
            $this->testGetById();
            $this->testGetBySlug();
            $this->testGetList();
            $this->testGetMenuTree();
            $this->testGetChildren();
            $this->testBreadcrumb();
            $this->testMoveCategory();
            $this->testToggleShow();
            $this->testValidation();
            $this->testTypeConstraints();
            
            echo "\n=== 所有测试通过！ ===\n";
        } catch (\Exception $e) {
            echo "\n=== 测试失败：" . $e->getMessage() . " ===\n";
            echo "文件：" . $e->getFile() . ":" . $e->getLine() . "\n";
        }
    }
    
    /**
     * 测试创建分类
     */
    private function testCreateCategory(): void
    {
        echo "测试创建分类...\n";
        
        $data = [
            'parent_id' => 0,
            'name' => '测试分类',
            'slug' => 'test-category',
            'type' => ArticleCategoryBean::TYPE_LIST,
            'description' => '这是一个测试分类',
            'sort_order' => 100,
            'is_show' => 1,
            'seo_title' => '测试分类SEO标题'
        ];
        
        $category = $this->service->create($data);
        $this->testIds[] = $category->id;
        
        if ($category->id > 0) {
            echo "✓ 创建分类成功，ID: {$category->id}\n";
        } else {
            throw new \Exception("创建分类失败");
        }
    }
    
    /**
     * 测试创建子分类
     */
    private function testCreateChildCategory(): void
    {
        echo "测试创建子分类...\n";
        
        if (empty($this->testIds)) {
            throw new \Exception("没有父分类可用于测试");
        }
        
        $parentId = $this->testIds[0];
        
        $data = [
            'parent_id' => $parentId,
            'name' => '子分类',
            'slug' => 'child-category',
            'type' => ArticleCategoryBean::TYPE_SINGLE,
            'description' => '这是一个子分类',
            'sort_order' => 50
        ];
        
        $category = $this->service->create($data);
        $this->testIds[] = $category->id;
        
        if ($category->level === 2 && $category->parentId === $parentId) {
            echo "✓ 创建子分类成功，层级: {$category->level}\n";
        } else {
            throw new \Exception("子分类层级计算错误");
        }
    }
    
    /**
     * 测试更新分类
     */
    private function testUpdateCategory(): void
    {
        echo "测试更新分类...\n";
        
        if (empty($this->testIds)) {
            throw new \Exception("没有分类可用于测试");
        }
        
        $id = $this->testIds[0];
        $data = [
            'name' => '更新后的分类名称',
            'description' => '更新后的描述'
        ];
        
        $category = $this->service->update($id, $data);
        
        if ($category->name === '更新后的分类名称') {
            echo "✓ 更新分类成功\n";
        } else {
            throw new \Exception("更新分类失败");
        }
    }
    
    /**
     * 测试根据ID获取分类
     */
    private function testGetById(): void
    {
        echo "测试根据ID获取分类...\n";
        
        if (empty($this->testIds)) {
            throw new \Exception("没有分类可用于测试");
        }
        
        $id = $this->testIds[0];
        $category = $this->service->getById($id);
        
        if ($category && $category->id === $id) {
            echo "✓ 根据ID获取分类成功\n";
        } else {
            throw new \Exception("根据ID获取分类失败");
        }
    }
    
    /**
     * 测试根据slug获取分类
     */
    private function testGetBySlug(): void
    {
        echo "测试根据slug获取分类...\n";
        
        $category = $this->service->getBySlug('test-category');
        
        if ($category && $category->slug === 'test-category') {
            echo "✓ 根据slug获取分类成功\n";
        } else {
            echo "! 根据slug获取分类失败或分类不存在\n";
        }
    }
    
    /**
     * 测试获取分类列表
     */
    private function testGetList(): void
    {
        echo "测试获取分类列表...\n";
        
        $result = $this->service->getList(1, 10);
        
        if (isset($result['list']) && is_array($result['list'])) {
            echo "✓ 获取分类列表成功，总数: {$result['total']}\n";
        } else {
            throw new \Exception("获取分类列表失败");
        }
    }
    
    /**
     * 测试获取菜单树
     */
    private function testGetMenuTree(): void
    {
        echo "测试获取菜单树...\n";
        
        $tree = $this->service->getMenuTree();
        
        if (is_array($tree)) {
            echo "✓ 获取菜单树成功，顶级分类数: " . count($tree) . "\n";
        } else {
            throw new \Exception("获取菜单树失败");
        }
    }
    
    /**
     * 测试获取子分类
     */
    private function testGetChildren(): void
    {
        echo "测试获取子分类...\n";
        
        if (empty($this->testIds)) {
            throw new \Exception("没有分类可用于测试");
        }
        
        $parentId = $this->testIds[0];
        $children = $this->service->getChildren($parentId);
        
        echo "✓ 获取子分类成功，数量: " . count($children) . "\n";
    }
    
    /**
     * 测试面包屑路径
     */
    private function testBreadcrumb(): void
    {
        echo "测试面包屑路径...\n";
        
        if (count($this->testIds) < 2) {
            echo "! 跳过面包屑测试（需要至少2个分类）\n";
            return;
        }
        
        $childId = $this->testIds[1];
        $breadcrumb = $this->service->getBreadcrumb($childId);
        
        echo "✓ 获取面包屑路径成功，层级数: " . count($breadcrumb) . "\n";
    }
    
    /**
     * 测试移动分类
     */
    private function testMoveCategory(): void
    {
        echo "测试移动分类...\n";
        
        if (count($this->testIds) < 2) {
            echo "! 跳过移动测试（需要至少2个分类）\n";
            return;
        }
        
        $childId = $this->testIds[1];
        $result = $this->service->move($childId, 0); // 移动到顶级
        
        if ($result) {
            echo "✓ 移动分类成功\n";
        } else {
            throw new \Exception("移动分类失败");
        }
    }
    
    /**
     * 测试切换显示状态
     */
    private function testToggleShow(): void
    {
        echo "测试切换显示状态...\n";
        
        if (empty($this->testIds)) {
            throw new \Exception("没有分类可用于测试");
        }
        
        $id = $this->testIds[0];
        $result = $this->service->toggleShow($id);
        
        if ($result) {
            echo "✓ 切换显示状态成功\n";
        } else {
            throw new \Exception("切换显示状态失败");
        }
    }
    
    /**
     * 测试数据验证
     */
    private function testValidation(): void
    {
        echo "测试数据验证...\n";
        
        // 测试空名称
        try {
            $this->service->create(['name' => '', 'slug' => 'test']);
            throw new \Exception("应该抛出名称为空的异常");
        } catch (\think\exception\ValidateException $e) {
            echo "✓ 空名称验证正确\n";
        }
        
        // 测试空slug
        try {
            $this->service->create(['name' => '测试', 'slug' => '']);
            throw new \Exception("应该抛出slug为空的异常");
        } catch (\think\exception\ValidateException $e) {
            echo "✓ 空slug验证正确\n";
        }
        
        // 测试重复slug
        try {
            $this->service->create(['name' => '测试2', 'slug' => 'test-category']);
            throw new \Exception("应该抛出slug重复的异常");
        } catch (\think\exception\ValidateException $e) {
            echo "✓ 重复slug验证正确\n";
        }
    }
    
    /**
     * 测试类型约束
     */
    private function testTypeConstraints(): void
    {
        echo "测试类型约束...\n";
        
        // 创建一个single类型的分类
        $data = [
            'parent_id' => 0,
            'name' => '单章分类',
            'slug' => 'single-category',
            'type' => ArticleCategoryBean::TYPE_SINGLE
        ];
        
        $singleCategory = $this->service->create($data);
        $this->testIds[] = $singleCategory->id;
        
        // 尝试在single类型下创建子分类
        try {
            $childData = [
                'parent_id' => $singleCategory->id,
                'name' => '子分类',
                'slug' => 'child-of-single'
            ];
            $this->service->create($childData);
            throw new \Exception("应该抛出类型约束异常");
        } catch (\think\exception\ValidateException $e) {
            echo "✓ 类型约束验证正确\n";
        }
    }
    
    /**
     * 清理测试数据
     */
    public function cleanup(): void
    {
        echo "\n清理测试数据...\n";
        
        try {
            // 按照创建的逆序删除（先删除子分类）
            $ids = array_reverse($this->testIds);
            foreach ($ids as $id) {
                $this->service->delete($id);
            }
            echo "✓ 测试数据清理完成\n";
        } catch (\Exception $e) {
            echo "! 清理测试数据失败: " . $e->getMessage() . "\n";
        }
    }
}

// 如果直接运行此文件，执行测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new ArticleCategoryServiceTest();
    $test->runAllTests();
    $test->cleanup();
}
