<?php
declare(strict_types=1);

namespace test;

use app\api\bean\ArticleCategoryBean;
use app\api\repository\ArticleCategoryRepository;

/**
 * 文章分类Repository测试类
 */
class ArticleCategoryRepositoryTest
{
    private ArticleCategoryRepository $repository;
    
    public function __construct()
    {
        $this->repository = new ArticleCategoryRepository();
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests(): void
    {
        echo "=== 文章分类Repository测试开始 ===\n\n";
        
        try {
            $this->testCreateCategory();
            $this->testFindById();
            $this->testFindBySlug();
            $this->testFindByParentId();
            $this->testExistsBySlug();
            $this->testHasChildren();
            $this->testUpdateCategory();
            $this->testGetList();
            $this->testFindAll();
            $this->testBreadcrumb();
            $this->testCircularReference();
            
            echo "\n=== 所有测试通过！ ===\n";
        } catch (\Exception $e) {
            echo "\n=== 测试失败：" . $e->getMessage() . " ===\n";
            echo "文件：" . $e->getFile() . ":" . $e->getLine() . "\n";
        }
    }
    
    /**
     * 测试创建分类
     */
    private function testCreateCategory(): void
    {
        echo "测试创建分类...\n";
        
        // 创建顶级分类
        $category = new ArticleCategoryBean();
        $category->parentId = 0;
        $category->name = '测试分类';
        $category->slug = 'test-category';
        $category->type = ArticleCategoryBean::TYPE_LIST;
        $category->description = '这是一个测试分类';
        $category->sortOrder = 100;
        $category->level = 1;
        $category->path = '1';
        $category->isShow = ArticleCategoryBean::SHOW_VISIBLE;
        
        $id = $this->repository->create($category);
        
        if ($id > 0) {
            echo "✓ 创建分类成功，ID: {$id}\n";
        } else {
            throw new \Exception("创建分类失败");
        }
    }
    
    /**
     * 测试根据ID查找
     */
    private function testFindById(): void
    {
        echo "测试根据ID查找分类...\n";
        
        $category = $this->repository->findById(1);
        
        if ($category && !$category->isEmpty()) {
            echo "✓ 根据ID查找成功，分类名称: {$category->name}\n";
        } else {
            echo "! 根据ID查找失败或分类不存在\n";
        }
    }
    
    /**
     * 测试根据slug查找
     */
    private function testFindBySlug(): void
    {
        echo "测试根据slug查找分类...\n";
        
        $category = $this->repository->findBySlug('test-category');
        
        if ($category && !$category->isEmpty()) {
            echo "✓ 根据slug查找成功，分类名称: {$category->name}\n";
        } else {
            echo "! 根据slug查找失败或分类不存在\n";
        }
    }
    
    /**
     * 测试根据父级ID查找子分类
     */
    private function testFindByParentId(): void
    {
        echo "测试根据父级ID查找子分类...\n";
        
        $children = $this->repository->findByParentId(0);
        
        echo "✓ 找到 " . count($children) . " 个顶级分类\n";
    }
    
    /**
     * 测试slug是否存在
     */
    private function testExistsBySlug(): void
    {
        echo "测试slug是否存在...\n";
        
        $exists = $this->repository->existsBySlug('test-category');
        
        if ($exists) {
            echo "✓ slug存在检查正确\n";
        } else {
            echo "! slug存在检查失败\n";
        }
        
        $notExists = $this->repository->existsBySlug('non-existent-slug');
        
        if (!$notExists) {
            echo "✓ slug不存在检查正确\n";
        } else {
            echo "! slug不存在检查失败\n";
        }
    }
    
    /**
     * 测试是否有子分类
     */
    private function testHasChildren(): void
    {
        echo "测试是否有子分类...\n";
        
        $hasChildren = $this->repository->hasChildren(1);
        
        echo "✓ 子分类检查完成，结果: " . ($hasChildren ? '有' : '无') . "\n";
    }
    
    /**
     * 测试更新分类
     */
    private function testUpdateCategory(): void
    {
        echo "测试更新分类...\n";
        
        $category = $this->repository->findById(1);
        
        if ($category && !$category->isEmpty()) {
            $category->description = '更新后的描述';
            $result = $this->repository->update($category);
            
            if ($result) {
                echo "✓ 更新分类成功\n";
            } else {
                echo "! 更新分类失败\n";
            }
        } else {
            echo "! 找不到要更新的分类\n";
        }
    }
    
    /**
     * 测试获取分类列表
     */
    private function testGetList(): void
    {
        echo "测试获取分类列表...\n";
        
        $result = $this->repository->getList(1, 10);
        
        echo "✓ 获取分类列表成功，总数: {$result['total']}\n";
    }
    
    /**
     * 测试获取所有分类
     */
    private function testFindAll(): void
    {
        echo "测试获取所有分类...\n";
        
        $categories = $this->repository->findAll();
        
        echo "✓ 获取所有分类成功，数量: " . count($categories) . "\n";
    }
    
    /**
     * 测试面包屑路径
     */
    private function testBreadcrumb(): void
    {
        echo "测试面包屑路径...\n";
        
        $breadcrumb = $this->repository->getBreadcrumb(1);
        
        echo "✓ 获取面包屑路径成功，层级数: " . count($breadcrumb) . "\n";
    }
    
    /**
     * 测试循环引用检查
     */
    private function testCircularReference(): void
    {
        echo "测试循环引用检查...\n";
        
        // 测试自引用
        $circular = $this->repository->checkCircularReference(1, 1);
        
        if ($circular) {
            echo "✓ 循环引用检查正确（自引用）\n";
        } else {
            echo "! 循环引用检查失败（自引用）\n";
        }
        
        // 测试正常引用
        $normal = $this->repository->checkCircularReference(1, 0);
        
        if (!$normal) {
            echo "✓ 循环引用检查正确（正常引用）\n";
        } else {
            echo "! 循环引用检查失败（正常引用）\n";
        }
    }
    
    /**
     * 清理测试数据
     */
    public function cleanup(): void
    {
        echo "\n清理测试数据...\n";
        
        try {
            // 这里可以添加清理测试数据的代码
            // $this->repository->delete(1);
            echo "✓ 测试数据清理完成\n";
        } catch (\Exception $e) {
            echo "! 清理测试数据失败: " . $e->getMessage() . "\n";
        }
    }
}

// 如果直接运行此文件，执行测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new ArticleCategoryRepositoryTest();
    $test->runAllTests();
    $test->cleanup();
}
