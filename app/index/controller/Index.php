<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\api\service\PortalConfigService;
use app\api\service\PortalModuleService;
use think\facade\View;
use think\facade\Db;

class Index extends BaseController
{

    /**
     * 测试页面
     */
    public function test()
    {
        return View::fetch('index/test', [
            'title' => '测试页面',
            'data' => [
                'timestamp' => time(),
                'random' => mt_rand(1000, 9999),
                'user_agent' => $this->request->header('user-agent')
            ]
        ]);
    }

    /**
     * 验证码测试
     */
    public function captcha()
    {
        return View::fetch('index/captcha', [
            'title' => '验证码测试'
        ]);
    }

    /**
     * 首页 - 门户页面
     */
    public function index()
    {
        try {
            $configService = new PortalConfigService();
            $moduleService = new PortalModuleService();

            // 获取基础配置，确保返回数组
            $basicConfigs = $configService->getConfigByGroup('basic') ?: [];
            $seoConfigs = $configService->getConfigByGroup('seo') ?: [];

            // 获取启用的模块，确保返回数组
            $modules = $moduleService->getEnabledModules() ?: [];

            // 获取新闻数据
            $newsData = $this->getNewsData();

            // 获取分类数据
            $categories = $this->getCategoryData();

            return View::fetch('index/index', [
                'basicConfigs' => $basicConfigs,
                'seoConfigs' => $seoConfigs,
                'modules' => $modules,
                'newsData' => $newsData,
                'categories' => $categories,
                'title' => $basicConfigs['site_name'] ?? '新闻门户网站'
            ]);

        } catch (\Exception $e) {
            // 如果出错，显示默认页面
            return View::fetch('index/index', [
                'basicConfigs' => ['site_name' => '新闻门户网站'],
                'seoConfigs' => [],
                'modules' => [],
                'newsData' => ['banner' => [], 'hot' => [], 'latest' => []],
                'categories' => [],
                'title' => '新闻门户网站',
                'error' => '系统初始化中，请稍后访问或联系管理员'
            ]);
        }
    }

    /**
     * 获取新闻数据
     */
    private function getNewsData(): array
    {
        try {
            // 获取轮播新闻（最新5条有封面图的新闻）
            $bannerNews = Db::table('articles')
                ->alias('a')
                ->leftJoin('article_categories c', 'a.category_id = c.id')
                ->field('a.*, c.name as category_name')
                ->where('a.status', 'published')
                ->where('a.cover_image', '<>', '')
                ->where('a.cover_image', 'not null')
                ->order('a.publish_time', 'desc')
                ->limit(5)
                ->select()
                ->toArray();

            // 获取热门新闻（按浏览量排序）
            $hotNews = Db::table('articles')
                ->alias('a')
                ->leftJoin('article_categories c', 'a.category_id = c.id')
                ->field('a.*, c.name as category_name')
                ->where('a.status', 'published')
                ->order('a.view_count', 'desc')
                ->limit(10)
                ->select()
                ->toArray();

            // 获取最新文章
            $latestNews = Db::table('articles')
                ->alias('a')
                ->leftJoin('article_categories c', 'a.category_id = c.id')
                ->field('a.*, c.name as category_name')
                ->where('a.status', 'published')
                ->order('a.publish_time', 'desc')
                ->limit(12)
                ->select()
                ->toArray();

            return [
                'banner' => $bannerNews,
                'hot' => $hotNews,
                'latest' => $latestNews
            ];
        } catch (\Exception $e) {
            // 如果数据库查询失败，返回空数组
            return [
                'banner' => [],
                'hot' => [],
                'latest' => []
            ];
        }
    }

    /**
     * 获取分类数据
     */
    private function getCategoryData(): array
    {
        try {
            return Db::table('article_categories')
                ->where('is_show', 1)
                ->where('level', 1)  // 只获取一级分类
                ->order('sort_order', 'desc')
                ->limit(10)
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 分类页面
     */
    public function category()
    {
        $categoryId = (int)$this->request->param('id', 0);

        try {
            // 获取分类信息
            $category = Db::table('article_categories')
                ->where('id', $categoryId)
                ->where('is_show', 1)
                ->find();

            if (!$category) {
                return redirect('/');
            }

            // 获取该分类下的文章
            $articles = Db::table('articles')
                ->alias('a')
                ->leftJoin('article_categories c', 'a.category_id = c.id')
                ->field('a.*, c.name as category_name')
                ->where('a.category_id', $categoryId)
                ->where('a.status', 'published')
                ->order('a.publish_time', 'desc')
                ->limit(20)
                ->select();

            // 获取所有分类用于导航
            $categories = $this->getCategoryData();

            return View::fetch('index/category', [
                'category' => $category,
                'articles' => $articles,
                'categories' => $categories,
                'title' => $category['name'] . ' - 新闻列表'
            ]);

        } catch (\Exception $e) {
            return redirect('/');
        }
    }

    /**
     * 文章详情页面
     */
    public function article()
    {
        $articleId = (int)$this->request->param('id', 0);

        try {
            // 获取文章信息
            $article = Db::table('articles')
                ->alias('a')
                ->leftJoin('article_categories c', 'a.category_id = c.id')
                ->field('a.*, c.name as category_name, c.id as category_id')
                ->where('a.id', $articleId)
                ->where('a.status', 'published')
                ->find();

            if (!$article) {
                return redirect('/');
            }

            // 增加浏览量
            Db::table('articles')->where('id', $articleId)->inc('view_count')->update();
            $article['view_count'] = ($article['view_count'] ?? 0) + 1;

            // 获取文章的自定义字段
            $customFields = $this->getArticleCustomFields($articleId);

            // 获取相关文章（同分类的其他文章）
            $relatedArticles = Db::table('articles')
                ->alias('a')
                ->leftJoin('article_categories c', 'a.category_id = c.id')
                ->field('a.id, a.title, a.cover_image, a.publish_time, a.view_count, c.name as category_name')
                ->where('a.category_id', $article['category_id'])
                ->where('a.id', '<>', $articleId)
                ->where('a.status', 'published')
                ->order('a.publish_time', 'desc')
                ->limit(6)
                ->select()
                ->toArray();

            // 获取所有分类用于导航
            $categories = $this->getCategoryData();

            return View::fetch('index/article', [
                'article' => $article,
                'customFields' => $customFields,
                'relatedArticles' => $relatedArticles,
                'categories' => $categories,
                'title' => $article['title'] . ' - ' . $article['category_name']
            ]);

        } catch (\Exception $e) {
            return redirect('/');
        }
    }

    /**
     * 获取文章的自定义字段
     */
    private function getArticleCustomFields(int $articleId): array
    {
        try {
            $fields = Db::table('article_custom_field_values')
                ->alias('v')
                ->leftJoin('article_custom_fields f', 'v.field_id = f.id')
                ->field('f.name, f.field_key, f.field_type, v.field_value')
                ->where('v.article_id', $articleId)
                ->where('f.is_active', 1)
                ->order('f.sort_order', 'desc')
                ->select()
                ->toArray();

            $result = [];
            foreach ($fields as $field) {
                // 处理不同类型的字段值
                $value = $field['field_value'];
                if ($field['field_type'] === 'tag' && $value) {
                    // 标签类型，解析JSON数组
                    $tags = json_decode($value, true);
                    if (is_array($tags)) {
                        $value = $tags;
                    }
                }

                $result[$field['field_key']] = [
                    'name' => $field['name'],
                    'type' => $field['field_type'],
                    'value' => $value
                ];
            }

            return $result;
        } catch (\Exception $e) {
            return [];
        }
    }
}
