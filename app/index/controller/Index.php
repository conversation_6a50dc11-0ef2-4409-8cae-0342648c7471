<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\api\service\PortalConfigService;
use app\api\service\PortalModuleService;
use think\facade\View;
use think\facade\Db;

class Index extends BaseController
{

    /**
     * 测试页面
     */
    public function test()
    {
        return View::fetch('index/test', [
            'title' => '测试页面',
            'data' => [
                'timestamp' => time(),
                'random' => mt_rand(1000, 9999),
                'user_agent' => $this->request->header('user-agent')
            ]
        ]);
    }

    /**
     * 验证码测试
     */
    public function captcha()
    {
        return View::fetch('index/captcha', [
            'title' => '验证码测试'
        ]);
    }

    /**
     * 首页 - 门户页面
     */
    public function index()
    {
        try {
            $configService = new PortalConfigService();
            $moduleService = new PortalModuleService();

            // 获取基础配置，确保返回数组
            $basicConfigs = $configService->getConfigByGroup('basic') ?: [];
            $seoConfigs = $configService->getConfigByGroup('seo') ?: [];

            // 获取启用的模块，确保返回数组
            $modules = $moduleService->getEnabledModules() ?: [];

            // 获取新闻数据
            $newsData = $this->getNewsData();

            // 获取分类数据
            $categories = $this->getCategoryData();

            return View::fetch('index/index', [
                'basicConfigs' => $basicConfigs,
                'seoConfigs' => $seoConfigs,
                'modules' => $modules,
                'newsData' => $newsData,
                'categories' => $categories,
                'title' => $basicConfigs['site_name'] ?? '新闻门户网站'
            ]);

        } catch (\Exception $e) {
            // 如果出错，显示默认页面
            return View::fetch('index/index', [
                'basicConfigs' => ['site_name' => '新闻门户网站'],
                'seoConfigs' => [],
                'modules' => [],
                'newsData' => ['banner' => [], 'hot' => [], 'latest' => []],
                'categories' => [],
                'title' => '新闻门户网站',
                'error' => '系统初始化中，请稍后访问或联系管理员'
            ]);
        }
    }

    /**
     * 获取新闻数据
     */
    private function getNewsData(): array
    {
        try {
            // 获取轮播新闻（最新5条有封面图的新闻）
            $bannerNews = Db::table('articles')
                ->alias('a')
                ->leftJoin('article_categories c', 'a.category_id = c.id')
                ->field('a.*, c.name as category_name')
                ->where('a.status', 'published')
                ->where('a.cover_image', '<>', '')
                ->where('a.cover_image', 'not null')
                ->order('a.publish_time', 'desc')
                ->limit(5)
                ->select()
                ->toArray();

            // 获取热门新闻（按浏览量排序）
            $hotNews = Db::table('articles')
                ->alias('a')
                ->leftJoin('article_categories c', 'a.category_id = c.id')
                ->field('a.*, c.name as category_name')
                ->where('a.status', 'published')
                ->order('a.view_count', 'desc')
                ->limit(10)
                ->select()
                ->toArray();

            // 获取最新文章
            $latestNews = Db::table('articles')
                ->alias('a')
                ->leftJoin('article_categories c', 'a.category_id = c.id')
                ->field('a.*, c.name as category_name')
                ->where('a.status', 'published')
                ->order('a.publish_time', 'desc')
                ->limit(12)
                ->select()
                ->toArray();

            return [
                'banner' => $bannerNews,
                'hot' => $hotNews,
                'latest' => $latestNews
            ];
        } catch (\Exception $e) {
            // 如果数据库查询失败，返回空数组
            return [
                'banner' => [],
                'hot' => [],
                'latest' => []
            ];
        }
    }

    /**
     * 获取分类数据
     */
    private function getCategoryData(): array
    {
        try {
            return Db::table('article_categories')
                ->where('is_show', 1)
                ->where('level', 1)  // 只获取一级分类
                ->order('sort_order', 'desc')
                ->limit(10)
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 分类页面
     */
    public function category()
    {
        $categoryId = (int)$this->request->param('id', 0);

        try {
            // 获取分类信息
            $category = Db::table('article_categories')
                ->where('id', $categoryId)
                ->where('is_show', 1)
                ->find();

            if (!$category) {
                return redirect('/');
            }

            // 获取该分类下的文章
            $articles = Db::table('articles')
                ->alias('a')
                ->leftJoin('article_categories c', 'a.category_id = c.id')
                ->field('a.*, c.name as category_name')
                ->where('a.category_id', $categoryId)
                ->where('a.status', 'published')
                ->order('a.publish_time', 'desc')
                ->paginate(20);

            // 获取所有分类用于导航
            $categories = $this->getCategoryData();

            return View::fetch('index/category', [
                'category' => $category,
                'articles' => $articles,
                'categories' => $categories,
                'title' => $category['name'] . ' - 新闻列表'
            ]);

        } catch (\Exception $e) {
            return redirect('/');
        }
    }
}
