<?php

namespace app\api\service;

use app\api\model\PortalConfig;
use think\facade\Cache;
use think\exception\ValidateException;

/**
 * 门户配置服务类
 */
class PortalConfigService
{
    // 缓存前缀
    const CACHE_PREFIX = 'portal_config:';
    
    // 缓存过期时间（秒）
    const CACHE_EXPIRE = 3600;
    
    /**
     * 获取配置列表（分页）
     *
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $group 分组筛选
     * @param string $keyword 关键词搜索
     * @return array
     */
    public function getConfigList(int $page = 1, int $limit = 20, string $group = '', string $keyword = ''): array
    {
        $query = PortalConfig::order('group_name,sort_order');
        
        // 分组筛选
        if (!empty($group)) {
            $query->where('group_name', $group);
        }
        
        // 关键词搜索
        if (!empty($keyword)) {
            $query->where(function($q) use ($keyword) {
                $q->whereLike('config_key', "%{$keyword}%")
                  ->whereOr('description', 'like', "%{$keyword}%");
            });
        }
        
        $result = $query->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);
        
        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'page' => $page,
            'limit' => $limit,
        ];
    }
    
    /**
     * 获取配置分组列表
     *
     * @return array
     */
    public function getConfigGroups(): array
    {
        $groups = PortalConfig::field('group_name')
                            ->group('group_name')
                            ->order('group_name')
                            ->column('group_name');
        
        return $groups ?: [];
    }
    
    /**
     * 根据ID获取配置
     *
     * @param int $id 配置ID
     * @return PortalConfig|null
     */
    public function getConfigById(int $id): ?PortalConfig
    {
        return PortalConfig::find($id);
    }
    
    /**
     * 根据键名获取配置值
     *
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @param bool $useCache 是否使用缓存
     * @return mixed
     */
    public function getConfigValue(string $key, $default = null, bool $useCache = true)
    {
        if ($useCache) {
            $cacheKey = self::CACHE_PREFIX . $key;
            $value = Cache::get($cacheKey);
            
            if ($value !== false) {
                return $value;
            }
        }
        
        $value = PortalConfig::getValue($key, $default);
        
        if ($useCache) {
            Cache::set($cacheKey, $value, self::CACHE_EXPIRE);
        }
        
        return $value;
    }
    
    /**
     * 根据分组获取配置
     *
     * @param string $group 分组名称
     * @param bool $useCache 是否使用缓存
     * @return array
     */
    public function getConfigByGroup(string $group, bool $useCache = true): array
    {
        if ($useCache) {
            $cacheKey = self::CACHE_PREFIX . 'group:' . $group;
            $configs = Cache::get($cacheKey);

            if ($configs !== false && is_array($configs)) {
                return $configs;
            }
        }

        $configs = PortalConfig::getByGroup($group);

        // 确保返回数组类型
        if (!is_array($configs)) {
            $configs = [];
        }

        if ($useCache) {
            Cache::set($cacheKey, $configs, self::CACHE_EXPIRE);
        }

        return $configs;
    }
    
    /**
     * 创建配置
     *
     * @param array $data 配置数据
     * @return PortalConfig
     * @throws ValidateException
     */
    public function createConfig(array $data): PortalConfig
    {
        // 数据验证
        $this->validateConfigData($data);
        
        // 检查键名是否已存在
        if (PortalConfig::where('config_key', $data['config_key'])->find()) {
            throw new ValidateException('配置键名已存在');
        }
        
        // 设置默认值
        $data['sort_order'] = $data['sort_order'] ?? 0;
        $data['group_name'] = $data['group_name'] ?? 'default';
        $data['config_type'] = $data['config_type'] ?? 'string';
        $data['is_system'] = $data['is_system'] ?? false;
        
        $config = PortalConfig::create($data);
        
        // 清除相关缓存
        $this->clearConfigCache($data['config_key'], $data['group_name']);
        
        return $config;
    }
    
    /**
     * 更新配置
     *
     * @param int $id 配置ID
     * @param array $data 配置数据
     * @return PortalConfig
     * @throws ValidateException
     */
    public function updateConfig(int $id, array $data): PortalConfig
    {
        $config = PortalConfig::find($id);
        if (!$config) {
            throw new ValidateException('配置不存在');
        }
        
        // 系统配置的键名不允许修改
        if ($config->is_system && isset($data['config_key']) && $data['config_key'] !== $config->config_key) {
            throw new ValidateException('系统配置的键名不允许修改');
        }
        
        // 验证数据
        $this->validateConfigData($data, $id);
        
        // 检查键名是否已存在（排除当前记录）
        if (isset($data['config_key']) && $data['config_key'] !== $config->config_key) {
            if (PortalConfig::where('config_key', $data['config_key'])->where('id', '<>', $id)->find()) {
                throw new ValidateException('配置键名已存在');
            }
        }
        
        $oldKey = $config->config_key;
        $oldGroup = $config->group_name;
        
        $config->save($data);
        
        // 清除相关缓存
        $this->clearConfigCache($oldKey, $oldGroup);
        if (isset($data['config_key']) && $data['config_key'] !== $oldKey) {
            $this->clearConfigCache($data['config_key'], $data['group_name'] ?? $config->group_name);
        }
        
        return $config;
    }
    
    /**
     * 删除配置
     *
     * @param int $id 配置ID
     * @return bool
     * @throws ValidateException
     */
    public function deleteConfig(int $id): bool
    {
        $config = PortalConfig::find($id);
        if (!$config) {
            throw new ValidateException('配置不存在');
        }
        
        if ($config->is_system) {
            throw new ValidateException('系统配置不允许删除');
        }
        
        $key = $config->config_key;
        $group = $config->group_name;
        
        $result = $config->delete();
        
        if ($result) {
            // 清除相关缓存
            $this->clearConfigCache($key, $group);
        }
        
        return $result;
    }
    
    /**
     * 批量设置配置
     *
     * @param array $configs 配置数组 [key => value, ...]
     * @param string $group 分组名称
     * @return bool
     */
    public function batchSetConfigs(array $configs, string $group = 'default'): bool
    {
        try {
            PortalConfig::startTrans();
            
            foreach ($configs as $key => $value) {
                $type = $this->guessConfigType($value);
                PortalConfig::setValue($key, $value, $type, $group);
            }
            
            PortalConfig::commit();
            
            // 清除分组缓存
            $this->clearGroupCache($group);
            
            return true;
        } catch (\Exception $e) {
            PortalConfig::rollback();
            throw new ValidateException('批量设置配置失败：' . $e->getMessage());
        }
    }
    
    /**
     * 验证配置数据
     *
     * @param array $data 配置数据
     * @param int $excludeId 排除的ID
     * @throws ValidateException
     */
    protected function validateConfigData(array $data, int $excludeId = 0): void
    {
        // 必填字段验证
        if (empty($data['config_key'])) {
            throw new ValidateException('配置键名不能为空');
        }
        
        // 键名格式验证
        if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_]*$/', $data['config_key'])) {
            throw new ValidateException('配置键名格式不正确，只能包含字母、数字和下划线，且必须以字母开头');
        }
        
        // 配置类型验证
        if (isset($data['config_type']) && !in_array($data['config_type'], ['string', 'number', 'boolean', 'json'])) {
            throw new ValidateException('配置类型不正确');
        }
        
        // JSON格式验证
        if (isset($data['config_type']) && $data['config_type'] === 'json' && isset($data['config_value'])) {
            if (!is_array($data['config_value']) && json_decode($data['config_value']) === null) {
                throw new ValidateException('JSON格式配置值不正确');
            }
        }
    }
    
    /**
     * 猜测配置类型
     *
     * @param mixed $value 配置值
     * @return string
     */
    protected function guessConfigType($value): string
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_numeric($value)) {
            return 'number';
        } elseif (is_array($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }
    
    /**
     * 清除配置缓存
     *
     * @param string $key 配置键名
     * @param string $group 分组名称
     */
    protected function clearConfigCache(string $key, string $group): void
    {
        Cache::delete(self::CACHE_PREFIX . $key);
        Cache::delete(self::CACHE_PREFIX . 'group:' . $group);
    }
    
    /**
     * 清除分组缓存
     *
     * @param string $group 分组名称
     */
    protected function clearGroupCache(string $group): void
    {
        Cache::delete(self::CACHE_PREFIX . 'group:' . $group);
    }
    
    /**
     * 清除所有配置缓存
     */
    public function clearAllCache(): void
    {
        Cache::clear();
    }
}
