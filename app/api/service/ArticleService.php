<?php
declare(strict_types=1);

namespace app\api\service;

use app\api\bean\ArticleBean;
use app\api\repository\ArticleRepository;
use app\api\repository\ArticleCustomFieldRepository;
use app\api\repository\ArticleCustomFieldValueRepository;
use think\exception\ValidateException;

/**
 * 文章Service
 */
class ArticleService
{
    private ArticleRepository $articleRepository;
    private ArticleCustomFieldRepository $fieldRepository;
    private ArticleCustomFieldValueRepository $valueRepository;

    public function __construct()
    {
        $this->articleRepository = new ArticleRepository();
        $this->fieldRepository = new ArticleCustomFieldRepository();
        $this->valueRepository = new ArticleCustomFieldValueRepository();
    }

    /**
     * 获取文章列表
     */
    public function getList(array $where = [], int $page = 1, int $limit = 15, bool $includeCustomFields = false): array
    {
        $result = $this->articleRepository->findWithPagination($where, $page, $limit);
        
        if ($includeCustomFields && !empty($result['list'])) {
            $result['list'] = $this->attachCustomFields($result['list']);
        }
        
        return $result;
    }

    /**
     * 根据ID获取文章详情
     */
    public function getById(int $id, bool $includeCustomFields = true): ?ArticleBean
    {
        $article = $this->articleRepository->findById($id);
        
        if ($article && $includeCustomFields) {
            $article = $this->attachCustomFields([$article])[0];
        }
        
        return $article;
    }

    /**
     * 根据slug获取文章详情
     */
    public function getBySlug(string $slug, bool $includeCustomFields = true): ?ArticleBean
    {
        $article = $this->articleRepository->findBySlug($slug);
        
        if ($article && $includeCustomFields) {
            $article = $this->attachCustomFields([$article])[0];
        }
        
        return $article;
    }

    /**
     * 创建文章
     */
    public function create(array $data): ArticleBean
    {
        // 验证基础数据
        $this->validateArticleData($data);
        
        // 提取自定义字段数据
        $customFields = $data['custom_fields'] ?? [];
        unset($data['custom_fields']);
        
        // 创建文章Bean
        $article = new ArticleBean();
        $this->fillArticleBean($article, $data);
        
        // 验证slug唯一性
        if (!$this->articleRepository->isSlugUnique($article->slug)) {
            throw new ValidateException('文章别名已存在');
        }
        
        // 保存文章
        $articleId = $this->articleRepository->create($article);
        
        // 保存自定义字段
        if (!empty($customFields)) {
            $this->saveCustomFields($articleId, $customFields);
        }
        
        return $this->getById($articleId);
    }

    /**
     * 更新文章
     */
    public function update(int $id, array $data): ArticleBean
    {
        $article = $this->articleRepository->findById($id);
        if (!$article) {
            throw new ValidateException('文章不存在');
        }
        
        // 验证基础数据
        $this->validateArticleData($data, $id);
        
        // 提取自定义字段数据
        $customFields = $data['custom_fields'] ?? [];
        unset($data['custom_fields']);
        
        // 更新文章Bean
        $this->fillArticleBean($article, $data);
        
        // 验证slug唯一性
        if (isset($data['slug']) && !$this->articleRepository->isSlugUnique($article->slug, $id)) {
            throw new ValidateException('文章别名已存在');
        }
        
        // 保存文章
        $this->articleRepository->update($article);
        
        // 更新自定义字段
        if (array_key_exists('custom_fields', $data)) {
            $this->saveCustomFields($id, $customFields);
        }
        
        return $this->getById($id);
    }

    /**
     * 删除文章
     */
    public function delete(int $id): bool
    {
        $article = $this->articleRepository->findById($id);
        if (!$article) {
            throw new ValidateException('文章不存在');
        }
        
        // 删除自定义字段值
        $this->valueRepository->deleteByArticleId($id);
        
        // 删除文章
        return $this->articleRepository->delete($id);
    }

    /**
     * 切换置顶状态
     */
    public function toggleTop(int $id): bool
    {
        return $this->articleRepository->toggleTop($id);
    }

    /**
     * 切换文章状态
     */
    public function changeStatus(int $id, string $status): bool
    {
        return $this->articleRepository->changeStatus($id, $status);
    }

    /**
     * 增加浏览次数
     */
    public function incrementViewCount(int $id): bool
    {
        return $this->articleRepository->incrementViewCount($id);
    }

    /**
     * 获取文章统计信息
     */
    public function getStatistics(): array
    {
        return $this->articleRepository->getStatistics();
    }

    /**
     * 批量更新排序
     */
    public function updateSort(array $sortData): bool
    {
        return $this->articleRepository->updateSort($sortData);
    }

    /**
     * 验证文章数据
     */
    private function validateArticleData(array $data, int $excludeId = 0): void
    {
        // 必填字段验证
        if (empty($data['title'])) {
            throw new ValidateException('文章标题不能为空');
        }
        
        if (empty($data['content'])) {
            throw new ValidateException('文章内容不能为空');
        }
        
        if (empty($data['category_id'])) {
            throw new ValidateException('文章分类不能为空');
        }
        
        // 状态验证
        if (isset($data['status']) && !in_array($data['status'], [
            ArticleBean::STATUS_DRAFT, 
            ArticleBean::STATUS_PUBLISHED, 
            ArticleBean::STATUS_ARCHIVED
        ])) {
            throw new ValidateException('无效的文章状态');
        }
    }

    /**
     * 填充文章Bean
     */
    private function fillArticleBean(ArticleBean $article, array $data): void
    {
        $fields = [
            'category_id' => 'categoryId',
            'title' => 'title',
            'slug' => 'slug',
            'summary' => 'summary',
            'content' => 'content',
            'author' => 'author',
            'cover_image' => 'coverImage',
            'status' => 'status',
            'is_top' => 'isTop',
            'sort_order' => 'sortOrder',
            'seo_title' => 'seoTitle',
            'seo_keywords' => 'seoKeywords',
            'seo_description' => 'seoDescription',
            'publish_time' => 'publishTime'
        ];
        
        foreach ($fields as $key => $property) {
            if (array_key_exists($key, $data)) {
                $article->$property = $data[$key];
            }
        }
        
        // 自动生成slug
        if (empty($article->slug) && !empty($article->title)) {
            $article->slug = $this->generateSlug($article->title);
        }
        
        // 设置默认值
        if (is_null($article->status)) {
            $article->status = ArticleBean::STATUS_DRAFT;
        }
        if (is_null($article->isTop)) {
            $article->isTop = ArticleBean::TOP_NO;
        }
        if (is_null($article->sortOrder)) {
            $article->sortOrder = 0;
        }
        if (is_null($article->viewCount)) {
            $article->viewCount = 0;
        }
    }

    /**
     * 生成slug
     */
    private function generateSlug(string $title): string
    {
        // 简单的slug生成逻辑，实际项目中可能需要更复杂的处理
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
        return substr($slug, 0, 100);
    }

    /**
     * 保存自定义字段
     */
    private function saveCustomFields(int $articleId, array $customFields): void
    {
        // 获取字段定义映射
        $fieldKeyMap = $this->fieldRepository->getFieldKeyMap(true);
        
        // 验证并保存字段值
        foreach ($customFields as $fieldKey => $fieldValue) {
            if (!isset($fieldKeyMap[$fieldKey])) {
                continue; // 跳过不存在的字段
            }
            
            $field = $fieldKeyMap[$fieldKey];
            
            // 验证字段值
            $errors = $field->validateFieldValue($fieldValue);
            if (!empty($errors)) {
                throw new ValidateException(implode(', ', $errors));
            }
            
            // 保存字段值
            $this->valueRepository->saveOrUpdate($articleId, $field->id, $fieldValue);
        }
    }

    /**
     * 为文章附加自定义字段
     */
    private function attachCustomFields(array $articles): array
    {
        if (empty($articles)) {
            return $articles;
        }
        
        // 获取所有文章ID
        $articleIds = array_map(function($article) {
            return $article->id;
        }, $articles);
        
        // 获取字段定义
        $fields = $this->fieldRepository->findActive();
        $fieldMap = [];
        foreach ($fields as $field) {
            $fieldMap[$field->id] = $field;
        }
        
        // 获取字段值
        foreach ($articles as $article) {
            $fieldValues = $this->valueRepository->getArticleFieldMap($article->id);
            $customFields = [];
            
            foreach ($fields as $field) {
                $value = $fieldValues[$field->id] ?? null;
                $customFields[$field->fieldKey] = [
                    'field' => $field->toArray(),
                    'value' => $value ? $value->getParsedValue($field->fieldType) : null,
                    'display_value' => $value ? $value->getDisplayValue($field->fieldType) : ''
                ];
            }
            
            $article->customFields = $customFields;
        }
        
        return $articles;
    }
}
