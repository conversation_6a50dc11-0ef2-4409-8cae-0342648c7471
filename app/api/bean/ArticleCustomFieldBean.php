<?php
declare(strict_types=1);

namespace app\api\bean;

use orm\annotation\Column;
use orm\annotation\Id;
use orm\annotation\Table;
use orm\BaseBean;

/**
 * 文章自定义字段Bean
 * @Table(name="article_custom_fields")
 */
class ArticleCustomFieldBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="name", type="string")
     */
    public mixed $name = null;

    /**
     * @Column(name="field_key", type="string")
     */
    public mixed $fieldKey = null;

    /**
     * @Column(name="field_type", type="string")
     */
    public mixed $fieldType = null;

    /**
     * @Column(name="is_required", type="integer")
     */
    public mixed $isRequired = null;

    /**
     * @Column(name="default_value", type="string")
     */
    public mixed $defaultValue = null;

    /**
     * @Column(name="options", type="string")
     */
    public mixed $options = null;

    /**
     * @Column(name="sort_order", type="integer")
     */
    public mixed $sortOrder = null;

    /**
     * @Column(name="is_active", type="integer")
     */
    public mixed $isActive = null;

    /**
     * @Column(name="description", type="string")
     */
    public mixed $description = null;

    /**
     * @Column(name="validation_rules", type="string")
     */
    public mixed $validationRules = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * @Column(name="update_time", type="datetime")
     */
    public mixed $updateTime = null;

    // 字段类型常量
    const TYPE_TAG = 'tag';           // 标签类型
    const TYPE_TEXT = 'text';         // 文本类型
    const TYPE_TEXTAREA = 'textarea'; // 长文本类型

    // 是否必填常量
    const REQUIRED_NO = 0;  // 非必填
    const REQUIRED_YES = 1; // 必填

    // 是否启用常量
    const ACTIVE_NO = 0;  // 禁用
    const ACTIVE_YES = 1; // 启用

    /**
     * 获取所有字段类型
     */
    public static function getFieldTypes(): array
    {
        return [
            self::TYPE_TAG => '标签',
            self::TYPE_TEXT => '文本',
            self::TYPE_TEXTAREA => '长文本'
        ];
    }

    /**
     * 获取字段类型文本
     */
    public function getFieldTypeText(): string
    {
        $types = self::getFieldTypes();
        return $types[$this->fieldType] ?? '未知';
    }

    /**
     * 获取必填状态文本
     */
    public function getRequiredText(): string
    {
        return $this->isRequired ? '是' : '否';
    }

    /**
     * 获取启用状态文本
     */
    public function getActiveText(): string
    {
        return $this->isActive ? '启用' : '禁用';
    }

    /**
     * 检查是否为标签类型
     */
    public function isTagType(): bool
    {
        return $this->fieldType === self::TYPE_TAG;
    }

    /**
     * 检查是否为文本类型
     */
    public function isTextType(): bool
    {
        return $this->fieldType === self::TYPE_TEXT;
    }

    /**
     * 检查是否为长文本类型
     */
    public function isTextareaType(): bool
    {
        return $this->fieldType === self::TYPE_TEXTAREA;
    }

    /**
     * 检查是否必填
     */
    public function isRequiredField(): bool
    {
        return $this->isRequired == self::REQUIRED_YES;
    }

    /**
     * 检查是否启用
     */
    public function isActiveField(): bool
    {
        return $this->isActive == self::ACTIVE_YES;
    }

    /**
     * 验证字段类型
     */
    public function validateFieldType(): bool
    {
        return in_array($this->fieldType, [self::TYPE_TAG, self::TYPE_TEXT, self::TYPE_TEXTAREA]);
    }

    /**
     * 获取选项配置（解析JSON）
     */
    public function getOptionsArray(): array
    {
        if (empty($this->options)) {
            return [];
        }
        
        $decoded = json_decode($this->options, true);
        return is_array($decoded) ? $decoded : [];
    }

    /**
     * 设置选项配置（转换为JSON）
     */
    public function setOptionsArray(array $options): void
    {
        $this->options = empty($options) ? null : json_encode($options, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 获取验证规则（解析JSON）
     */
    public function getValidationRulesArray(): array
    {
        if (empty($this->validationRules)) {
            return [];
        }
        
        $decoded = json_decode($this->validationRules, true);
        return is_array($decoded) ? $decoded : [];
    }

    /**
     * 设置验证规则（转换为JSON）
     */
    public function setValidationRulesArray(array $rules): void
    {
        $this->validationRules = empty($rules) ? null : json_encode($rules, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 验证字段值
     */
    public function validateFieldValue($value): array
    {
        $errors = [];
        
        // 必填验证
        if ($this->isRequiredField() && (is_null($value) || $value === '')) {
            $errors[] = $this->name . '为必填字段';
            return $errors;
        }
        
        // 如果值为空且非必填，则跳过其他验证
        if (is_null($value) || $value === '') {
            return $errors;
        }
        
        // 根据字段类型验证
        switch ($this->fieldType) {
            case self::TYPE_TAG:
                $errors = array_merge($errors, $this->validateTagValue($value));
                break;
            case self::TYPE_TEXT:
                $errors = array_merge($errors, $this->validateTextValue($value));
                break;
            case self::TYPE_TEXTAREA:
                $errors = array_merge($errors, $this->validateTextareaValue($value));
                break;
        }
        
        return $errors;
    }

    /**
     * 验证标签类型值
     */
    private function validateTagValue($value): array
    {
        $errors = [];
        
        // 标签类型应该是JSON数组格式
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errors[] = $this->name . '标签格式不正确';
                return $errors;
            }
            $value = $decoded;
        }
        
        if (!is_array($value)) {
            $errors[] = $this->name . '标签必须是数组格式';
            return $errors;
        }
        
        // 检查标签数量限制
        $options = $this->getOptionsArray();
        if (isset($options['max_tags']) && count($value) > $options['max_tags']) {
            $errors[] = $this->name . '标签数量不能超过' . $options['max_tags'] . '个';
        }
        
        return $errors;
    }

    /**
     * 验证文本类型值
     */
    private function validateTextValue($value): array
    {
        $errors = [];
        
        if (!is_string($value)) {
            $errors[] = $this->name . '必须是文本格式';
            return $errors;
        }
        
        // 检查长度限制
        $rules = $this->getValidationRulesArray();
        if (isset($rules['max_length']) && mb_strlen($value) > $rules['max_length']) {
            $errors[] = $this->name . '长度不能超过' . $rules['max_length'] . '个字符';
        }
        
        return $errors;
    }

    /**
     * 验证长文本类型值
     */
    private function validateTextareaValue($value): array
    {
        return $this->validateTextValue($value); // 与文本类型验证相同
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'field_key' => $this->fieldKey,
            'field_type' => $this->fieldType,
            'field_type_text' => $this->getFieldTypeText(),
            'is_required' => $this->isRequired,
            'required_text' => $this->getRequiredText(),
            'default_value' => $this->defaultValue,
            'options' => $this->getOptionsArray(),
            'sort_order' => $this->sortOrder,
            'is_active' => $this->isActive,
            'active_text' => $this->getActiveText(),
            'description' => $this->description,
            'validation_rules' => $this->getValidationRulesArray(),
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
            'is_tag_type' => $this->isTagType(),
            'is_text_type' => $this->isTextType(),
            'is_textarea_type' => $this->isTextareaType(),
            'is_required_field' => $this->isRequiredField(),
            'is_active_field' => $this->isActiveField()
        ];
    }
}
