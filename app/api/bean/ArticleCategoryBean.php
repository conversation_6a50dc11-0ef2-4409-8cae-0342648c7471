<?php
declare(strict_types=1);

namespace app\api\bean;

use orm\BaseBean;
use orm\mapping\Table;
use orm\mapping\Column;
use orm\mapping\Id;

/**
 * 文章分类Bean
 * @Table(name="article_categories")
 */
class ArticleCategoryBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="parent_id", type="integer")
     */
    public mixed $parentId = null;

    /**
     * @Column(name="name", type="string")
     */
    public mixed $name = null;

    /**
     * @Column(name="slug", type="string")
     */
    public mixed $slug = null;

    /**
     * @Column(name="type", type="string")
     */
    public mixed $type = null;

    /**
     * @Column(name="description", type="string")
     */
    public mixed $description = null;

    /**
     * @Column(name="link_url", type="string")
     */
    public mixed $linkUrl = null;

    /**
     * @Column(name="cover_image", type="string")
     */
    public mixed $coverImage = null;

    /**
     * @Column(name="sort_order", type="integer")
     */
    public mixed $sortOrder = null;

    /**
     * @Column(name="level", type="integer")
     */
    public mixed $level = null;

    /**
     * @Column(name="path", type="string")
     */
    public mixed $path = null;

    /**
     * @Column(name="is_show", type="integer")
     */
    public mixed $isShow = null;

    /**
     * @Column(name="seo_title", type="string")
     */
    public mixed $seoTitle = null;

    /**
     * @Column(name="seo_keywords", type="string")
     */
    public mixed $seoKeywords = null;

    /**
     * @Column(name="seo_description", type="string")
     */
    public mixed $seoDescription = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * @Column(name="update_time", type="datetime")
     */
    public mixed $updateTime = null;

    // 分类类型常量
    const TYPE_LIST = 'list';      // 列表类型，可以有子分类
    const TYPE_SINGLE = 'single';  // 单章类型，不能有子分类
    const TYPE_LINK = 'link';      // 连接类型，不能有子分类

    // 显示状态常量
    const SHOW_HIDDEN = 0;  // 隐藏
    const SHOW_VISIBLE = 1; // 显示

    /**
     * 获取所有分类类型
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_LIST => '列表',
            self::TYPE_SINGLE => '单章',
            self::TYPE_LINK => '连接'
        ];
    }

    /**
     * 获取类型文本
     */
    public function getTypeText(): string
    {
        $types = self::getTypes();
        return $types[$this->type] ?? '未知';
    }

    /**
     * 获取显示状态文本
     */
    public function getShowText(): string
    {
        return $this->isShow ? '显示' : '隐藏';
    }

    /**
     * 检查是否可以有子分类
     */
    public function canHaveChildren(): bool
    {
        return $this->type === self::TYPE_LIST;
    }

    /**
     * 检查是否为顶级分类
     */
    public function isTopLevel(): bool
    {
        return $this->parentId == 0;
    }

    /**
     * 获取路径数组
     */
    public function getPathArray(): array
    {
        if (empty($this->path)) {
            return [];
        }
        return array_map('intval', explode(',', $this->path));
    }

    /**
     * 设置路径数组
     */
    public function setPathArray(array $pathArray): void
    {
        $this->path = empty($pathArray) ? null : implode(',', $pathArray);
    }

    /**
     * 验证分类类型
     */
    public function validateType(): bool
    {
        return in_array($this->type, [self::TYPE_LIST, self::TYPE_SINGLE, self::TYPE_LINK]);
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'parent_id' => $this->parentId,
            'name' => $this->name,
            'slug' => $this->slug,
            'type' => $this->type,
            'type_text' => $this->getTypeText(),
            'description' => $this->description,
            'link_url' => $this->linkUrl,
            'cover_image' => $this->coverImage,
            'sort_order' => $this->sortOrder,
            'level' => $this->level,
            'path' => $this->path,
            'path_array' => $this->getPathArray(),
            'is_show' => $this->isShow,
            'show_text' => $this->getShowText(),
            'can_have_children' => $this->canHaveChildren(),
            'is_top_level' => $this->isTopLevel(),
            'seo_title' => $this->seoTitle,
            'seo_keywords' => $this->seoKeywords,
            'seo_description' => $this->seoDescription,
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
        ];
    }

    /**
     * 转换为菜单项格式
     */
    public function toMenuItem(): array
    {
        return [
            'id' => $this->id,
            'parent_id' => $this->parentId,
            'name' => $this->name,
            'slug' => $this->slug,
            'type' => $this->type,
            'link_url' => $this->linkUrl,
            'level' => $this->level,
            'sort_order' => $this->sortOrder,
            'children' => []
        ];
    }
}
