<?php
declare(strict_types=1);

namespace app\api\bean;

use orm\BaseBean;
use orm\mapping\Table;
use orm\mapping\Column;
use orm\mapping\Id;

/**
 * 管理员操作日志Bean
 * @Table(name="admin_logs")
 */
class AdminLogBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="user_id", type="integer")
     */
    public mixed $userId = null;

    /**
     * @Column(name="username", type="string")
     */
    public mixed $username = null;

    /**
     * @Column(name="action", type="string")
     */
    public mixed $action = null;

    /**
     * @Column(name="description", type="string")
     */
    public mixed $description = null;

    /**
     * @Column(name="ip", type="string")
     */
    public mixed $ip = null;

    /**
     * @Column(name="user_agent", type="string")
     */
    public mixed $userAgent = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->userId,
            'username' => $this->username,
            'action' => $this->action,
            'description' => $this->description,
            'ip' => $this->ip,
            'user_agent' => $this->userAgent,
            'create_time' => $this->createTime,
        ];
    }
}
