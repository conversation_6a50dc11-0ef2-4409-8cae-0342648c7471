<?php
declare(strict_types=1);

namespace app\api\test;

use app\api\bean\AdminUserBean;
use app\api\repository\AdminUserRepository;
use orm\EntityManager;

/**
 * Bean和Repository测试类
 */
class BeanTest
{
    /**
     * 测试Bean创建和基本功能
     */
    public function testBeanCreation()
    {
        echo "=== 测试Bean创建 ===\n";
        
        $user = new AdminUserBean();
        $user->username = 'test_user';
        $user->email = '<EMAIL>';
        $user->realName = '测试用户';
        $user->role = AdminUserBean::ROLE_CONTENT_ADMIN;
        $user->status = AdminUserBean::STATUS_ENABLED;
        $user->setPassword('123456');
        
        echo "用户名: " . $user->username . "\n";
        echo "邮箱: " . $user->email . "\n";
        echo "角色: " . $user->getRoleText() . "\n";
        echo "状态: " . $user->getStatusText() . "\n";
        echo "密码验证: " . ($user->checkPassword('123456') ? '通过' : '失败') . "\n";
        echo "Bean是否为空: " . ($user->isEmpty() ? '是' : '否') . "\n";
        
        return $user;
    }
    
    /**
     * 测试EntityManager解析
     */
    public function testEntityManagerParsing()
    {
        echo "\n=== 测试EntityManager解析 ===\n";
        
        try {
            $user = new AdminUserBean();
            $manager = EntityManager::create($user);
            $metaInfo = $manager->getMetaInfo();
            
            if ($metaInfo) {
                echo "表名: " . $metaInfo->tableName . "\n";
                echo "类名: " . $metaInfo->className . "\n";
                echo "字段数量: " . count($metaInfo->colArray) . "\n";
                echo "主键字段数量: " . count($metaInfo->idColArray) . "\n";
                
                echo "字段列表:\n";
                foreach ($metaInfo->colArray as $col) {
                    echo "  - {$col->name} -> {$col->colName} (PK: " . ($col->isPK ? '是' : '否') . ")\n";
                }
            } else {
                echo "解析失败：metaInfo为空\n";
            }
        } catch (\Exception $e) {
            echo "解析异常: " . $e->getMessage() . "\n";
            echo "异常文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
        }
    }
    
    /**
     * 测试Repository基本功能
     */
    public function testRepository()
    {
        echo "\n=== 测试Repository ===\n";
        
        try {
            $repository = new AdminUserRepository();
            
            // 测试查找用户（应该找到admin用户）
            $admin = $repository->findByUsername('admin');
            if ($admin) {
                echo "找到管理员用户:\n";
                echo "  ID: " . $admin->id . "\n";
                echo "  用户名: " . $admin->username . "\n";
                echo "  邮箱: " . $admin->email . "\n";
                echo "  角色: " . $admin->getRoleText() . "\n";
                echo "  状态: " . $admin->getStatusText() . "\n";
            } else {
                echo "未找到admin用户\n";
            }
            
            // 测试检查用户名是否存在
            $exists = $repository->existsByUsername('admin');
            echo "admin用户名是否存在: " . ($exists ? '是' : '否') . "\n";
            
        } catch (\Exception $e) {
            echo "Repository测试异常: " . $e->getMessage() . "\n";
            echo "异常文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
        }
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始Bean和Repository测试...\n\n";
        
        $this->testBeanCreation();
        $this->testEntityManagerParsing();
        $this->testRepository();
        
        echo "\n测试完成！\n";
    }
}
