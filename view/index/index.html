<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title}</title>
    <meta name="keywords" content="{$seoConfigs.site_keywords ?? '新闻,资讯,门户'}">
    <meta name="description" content="{$seoConfigs.site_description ?? '专业的新闻资讯门户网站'}">
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f5f5; }

        /* 头部样式 */
        .header {
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .header-top {
            background: #1890ff;
            color: #fff;
            padding: 8px 0;
            font-size: 12px;
        }
        .header-main {
            padding: 15px 0;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #1890ff;
            text-decoration: none;
        }
        .search-box {
            position: relative;
        }
        .search-input {
            width: 300px;
            height: 40px;
            border: 2px solid #1890ff;
            border-radius: 20px;
            padding: 0 50px 0 20px;
            outline: none;
        }
        .search-btn {
            position: absolute;
            right: 5px;
            top: 5px;
            width: 30px;
            height: 30px;
            background: #1890ff;
            border: none;
            border-radius: 15px;
            color: #fff;
            cursor: pointer;
        }

        /* 导航样式 */
        .nav-menu {
            background: #fff;
            border-top: 1px solid #e6e6e6;
            padding: 0;
        }
        .nav-item {
            display: inline-block;
            padding: 15px 25px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }
        .nav-item:hover, .nav-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }

        /* 主体内容 */
        .main-content {
            padding: 20px 0;
        }
        .module-container {
            margin-bottom: 30px;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .module-header {
            background: #fafafa;
            padding: 15px 20px;
            border-bottom: 1px solid #e6e6e6;
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .module-body {
            padding: 20px;
        }

        /* 轮播样式 */
        .banner-item {
            position: relative;
            height: 400px;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: flex-end;
        }
        .banner-content {
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: #fff;
            padding: 30px;
            width: 100%;
        }
        .banner-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        .banner-summary {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.6;
        }

        /* 新闻列表样式 */
        .news-list {
            list-style: none;
        }
        .news-item {
            display: flex;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s;
        }
        .news-item:hover {
            background: #fafafa;
            margin: 0 -20px;
            padding: 15px 20px;
        }
        .news-item:last-child {
            border-bottom: none;
        }
        .news-image {
            width: 120px;
            height: 80px;
            background-size: cover;
            background-position: center;
            border-radius: 6px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .news-content {
            flex: 1;
        }
        .news-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
            text-decoration: none;
        }
        .news-title:hover {
            color: #1890ff;
        }
        .news-meta {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }
        .news-summary {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 分类导航样式 */
        .category-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .category-item {
            padding: 10px 20px;
            background: #f8f9fa;
            border-radius: 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            border: 1px solid #e9ecef;
        }
        .category-item:hover {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }

        /* 广告样式 */
        .ad-container {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border: 2px dashed #ddd;
            border-radius: 8px;
            color: #999;
        }

        /* 外链模块样式 */
        .external-links {
            background: #f8f9fa;
            padding: 40px 0;
            margin-top: 50px;
            border-top: 1px solid #e6e6e6;
        }
        .links-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .links-header h3 {
            font-size: 24px;
            color: #333;
            margin: 0;
            font-weight: bold;
        }
        .links-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        .links-category {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .category-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #1890ff;
        }
        .links-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        .external-link {
            display: block;
            padding: 8px 12px;
            background: #f8f9fa;
            color: #333;
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s;
            font-size: 14px;
            border: 1px solid #e9ecef;
        }
        .external-link:hover {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
            transform: translateY(-1px);
        }

        /* 底部样式 */
        .footer {
            background: #333;
            color: #fff;
            padding: 20px 0;
        }
        .footer-bottom {
            text-align: center;
            color: #999;
            font-size: 12px;
        }
        .footer-bottom a {
            color: #999;
            text-decoration: none;
            margin: 0 5px;
        }
        .footer-bottom a:hover {
            color: #1890ff;
        }

        /* 响应式布局 */
        .module-one-column {
            width: 100%;
        }
        .module-two-column {
            width: 48%;
            display: inline-block;
            vertical-align: top;
            margin-right: 2%;
        }
        .module-two-column:nth-child(2n) {
            margin-right: 0;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .module-two-column {
                width: 100%;
                margin-right: 0;
                margin-bottom: 20px;
            }
            .search-input {
                width: 200px;
            }
            .banner-title {
                font-size: 18px;
            }
            .news-image {
                width: 80px;
                height: 60px;
            }
            .links-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .links-list {
                grid-template-columns: 1fr;
            }
            .external-links {
                padding: 20px 0;
            }
        }

        /* 错误提示样式 */
        .error-message {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="header-top">
            <div class="layui-container">
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <span>欢迎访问 {$basicConfigs.site_name ?? '新闻门户网站'}</span>
                    </div>
                    <div class="layui-col-md6" style="text-align: right;">
                        <span id="currentTime"></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="header-main">
            <div class="layui-container">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md4">
                        <a href="/" class="logo">{$basicConfigs.site_name ?? '新闻门户'}</a>
                    </div>
                    <div class="layui-col-md8" style="text-align: right;">
                        <div class="search-box">
                            <input type="text" class="search-input" placeholder="搜索新闻..." id="searchInput">
                            <button class="search-btn" onclick="searchNews()">
                                <i class="layui-icon layui-icon-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="nav-menu">
            <div class="layui-container">
                <a href="/" class="nav-item active">首页</a>
                {volist name="categories" id="category"}
                <a href="/category/{$category.id}" class="nav-item">{$category.name}</a>
                {/volist}
            </div>
        </nav>
    </header>

    <!-- 主体内容 -->
    <main class="main-content">
        <div class="layui-container">
            {if isset($error)}
            <div class="error-message">
                <strong>系统提示：</strong>{$error}
            </div>
            {/if}

            <!-- 动态渲染模块 -->
            {volist name="modules" id="module"}
            <div class="module-container module-{$module.column_count == 2 ? 'two-column' : 'one-column'}">
                {switch name="module.module_name"}
                    {case value="news_banner"}
                        {include file="modules/banner" /}
                    {/case}
                    {case value="hot_news"}
                        {include file="modules/hot_news" /}
                    {/case}
                    {case value="category_nav"}
                        {include file="modules/category_nav" /}
                    {/case}
                    {case value="latest_articles"}
                        {include file="modules/latest_articles" /}
                    {/case}
                    {case value="sidebar_ads"}
                        {include file="modules/sidebar_ads" /}
                    {/case}
                    {default /}
                        <div class="module-header">{$module.module_title}</div>
                        <div class="module-body">
                            <p>模块 "{$module.module_name}" 暂未实现</p>
                        </div>
                {/switch}
            </div>
            {/volist}

            {if empty($modules)}
            <div class="module-container">
                <div class="module-header">欢迎访问</div>
                <div class="module-body">
                    <p>门户配置系统正在加载中，请稍后...</p>
                    <p>如果您是管理员，请前往 <a href="/admin/portal_module.html">模块配置</a> 启用相关模块。</p>
                </div>
            </div>
            {/if}
        </div>
    </main>

    <!-- 外链模块 -->
    <section class="external-links">
        <div class="layui-container">
            <div class="links-header">
                <h3>相关链接</h3>
            </div>
            <div class="links-content">
                <div class="links-category">
                    <div class="category-title">新闻媒体</div>
                    <div class="links-list">
                        <a href="https://www.xinhuanet.com" target="_blank" class="external-link">新华网</a>
                        <a href="https://www.people.com.cn" target="_blank" class="external-link">人民网</a>
                        <a href="https://www.cctv.com" target="_blank" class="external-link">央视网</a>
                        <a href="https://www.chinanews.com.cn" target="_blank" class="external-link">中新网</a>
                        <a href="https://www.gmw.cn" target="_blank" class="external-link">光明网</a>
                        <a href="https://www.chinadaily.com.cn" target="_blank" class="external-link">中国日报</a>
                    </div>
                </div>

                <div class="links-category">
                    <div class="category-title">科技资讯</div>
                    <div class="links-list">
                        <a href="https://www.36kr.com" target="_blank" class="external-link">36氪</a>
                        <a href="https://www.ithome.com" target="_blank" class="external-link">IT之家</a>
                        <a href="https://www.cnbeta.com" target="_blank" class="external-link">cnBeta</a>
                        <a href="https://www.pingwest.com" target="_blank" class="external-link">PingWest</a>
                        <a href="https://www.geekpark.net" target="_blank" class="external-link">极客公园</a>
                        <a href="https://www.leiphone.com" target="_blank" class="external-link">雷锋网</a>
                    </div>
                </div>

                <div class="links-category">
                    <div class="category-title">财经金融</div>
                    <div class="links-list">
                        <a href="https://www.caixin.com" target="_blank" class="external-link">财新网</a>
                        <a href="https://www.yicai.com" target="_blank" class="external-link">第一财经</a>
                        <a href="https://www.jiemian.com" target="_blank" class="external-link">界面新闻</a>
                        <a href="https://www.wallstreetcn.com" target="_blank" class="external-link">华尔街见闻</a>
                        <a href="https://www.eastmoney.com" target="_blank" class="external-link">东方财富</a>
                        <a href="https://www.jrj.com.cn" target="_blank" class="external-link">金融界</a>
                    </div>
                </div>

                <div class="links-category">
                    <div class="category-title">开发者工具</div>
                    <div class="links-list">
                        <a href="https://github.com" target="_blank" class="external-link">GitHub</a>
                        <a href="https://gitee.com" target="_blank" class="external-link">Gitee</a>
                        <a href="https://stackoverflow.com" target="_blank" class="external-link">Stack Overflow</a>
                        <a href="https://www.runoob.com" target="_blank" class="external-link">菜鸟教程</a>
                        <a href="https://developer.mozilla.org" target="_blank" class="external-link">MDN</a>
                        <a href="https://www.w3school.com.cn" target="_blank" class="external-link">W3School</a>
                    </div>
                </div>

                <div class="links-category">
                    <div class="category-title">API服务</div>
                    <div class="links-list">
                        <a href="/api/portal/modules/enabled" target="_blank" class="external-link">门户模块API</a>
                        <a href="/api/admin/portal/configs/groups" target="_blank" class="external-link">配置分组API</a>
                        <a href="/api/admin/portal/configs/" target="_blank" class="external-link">配置列表API</a>
                        <a href="/api/admin/portal/modules/" target="_blank" class="external-link">模块管理API</a>
                        <a href="/admin/portal_config.html" target="_blank" class="external-link">配置管理后台</a>
                        <a href="/admin/portal_module.html" target="_blank" class="external-link">模块管理后台</a>
                    </div>
                </div>

                <div class="links-category">
                    <div class="category-title">实用工具</div>
                    <div class="links-list">
                        <a href="https://www.baidu.com" target="_blank" class="external-link">百度搜索</a>
                        <a href="https://translate.google.com" target="_blank" class="external-link">谷歌翻译</a>
                        <a href="https://www.tianqi.com" target="_blank" class="external-link">天气预报</a>
                        <a href="https://map.baidu.com" target="_blank" class="external-link">百度地图</a>
                        <a href="https://www.12306.cn" target="_blank" class="external-link">12306</a>
                        <a href="https://www.sf-express.com" target="_blank" class="external-link">顺丰速运</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 底部版权 -->
    <footer class="footer">
        <div class="layui-container">
            <div class="footer-bottom">
                <p>&copy; 2025 {$basicConfigs.site_name ?? '新闻门户网站'}. 版权所有 |
                   <a href="#" style="color: #999;">使用协议</a> |
                   <a href="#" style="color: #999;">隐私政策</a> |
                   <a href="#" style="color: #999;">联系我们</a>
                </p>
            </div>
        </div>
    </footer>

    <script src="/static/layui/layui.js"></script>
    <script>
        layui.use(['carousel', 'element'], function(){
            var carousel = layui.carousel;
            var element = layui.element;

            // 初始化轮播
            if (document.getElementById('newsBanner')) {
                carousel.render({
                    elem: '#newsBanner',
                    width: '100%',
                    height: '400px',
                    interval: 5000,
                    anim: 'fade'
                });
            }
        });

        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.getFullYear() + '年' +
                          (now.getMonth() + 1).toString().padStart(2, '0') + '月' +
                          now.getDate().toString().padStart(2, '0') + '日 ' +
                          now.getHours().toString().padStart(2, '0') + ':' +
                          now.getMinutes().toString().padStart(2, '0');
            document.getElementById('currentTime').textContent = timeStr;
        }

        // 搜索功能
        function searchNews() {
            const keyword = document.getElementById('searchInput').value.trim();
            if (keyword) {
                window.location.href = '/search?q=' + encodeURIComponent(keyword);
            }
        }

        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchNews();
            }
        });

        // 初始化
        updateTime();
        setInterval(updateTime, 60000); // 每分钟更新一次时间
    </script>
</body>
</html>
